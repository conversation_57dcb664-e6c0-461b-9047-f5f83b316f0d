![alt text](image.png)

1. 会员ID ：（双击ID可进入用户详情页）- userInfo.userID
2. 会员姓名：性别icon 以及realName （昵称） userInfo 里的字段，使用UserName展示 切换realName
3：被动方ID. （双击ID可进入用户详情页）- passiveInfo.userID
4. 被动方姓名：性别icon 以及realName （昵称） passiveInfo 里的字段，使用UserName展示 切换realName 
5. 当前进展：progress 颜色待确认 当前进展后面的微信icon 展示规则根据 isExchangeWeChat 字段判断是否展示
6. 当前进展停留时间：progressDuration 加“天”单位
7. 推给被动方时间： recommendToPassiveTime 转化为需要的时间格式
8. 被动方反馈（推卡）： 参考精选列表（被动方推荐反馈）接口调用需要增加一个id入参
9：牵线进展： passiveContactProgress
10. 推给会员时间： recommendToUserTime 转化为需要的时间格式
11. 会员反馈（推卡）： 参考精选列表（会员推荐反馈）接口调用需要增加一个id入参

12. 预约一见：
- 预约一见第一种展示逻辑: meetTime为空并且appointMeetTime为空 展示预约一件按钮
- 预约一见第二种展示逻辑: meetTime为空并且appointMeetTime为空 isPassiveProvideMeetTime 为true 展示icon及提示
- 预约一见第三种展示逻辑:  如果meetTime 有值展示 已一见
- 预约一见第四种展示逻辑: 如果meetTime为空  appointMeetTime有值展示 “约”标签 +时间


13. 一见： meetTime 如果有时间展示时间 没有时间展示 （再判断权限展示空还是确认一见的按钮） 权限：

14. 一见会员反馈： userMeetFeedback

15. 一见被动方反馈：passiveMeetFeedback

16. 交往：
- 交往第一种展示逻辑:判断是否处于交往状态（正在交往） isInRelationship true 展示交往时间+天数 （开始时间 lastRelationshipStartTime 需计算到今天过去了多少天）
- 交往第二种展示逻辑:isInRelationship 为false（不处于交往状态） 并且isInMarriage isInLove 都为false 展示已解除
- 交往第三种展示逻辑:isInRelationship 为false（不处于交往状态） 并且isInMarriage isInLove 有一个为true  展示时间
- 交往第四种展示逻辑是 isInRelationship 为false 并且lastRelationshipStartTime 为空


16. 恋爱： 
 - 恋爱第一种展示逻辑是 ：isInLove 为true 展示恋爱时间+天数（开始时间自己算 lastLoveStartTime）标签已确认 是根据isUserConfirmLove 为true
 - 恋爱第二种展示逻辑是 ：isInLove 为true 展示恋爱时间+天数（开始时间自己算 lastLoveStartTime）标签未确认 是根据isUserConfirmLove 为false 
 - 恋爱第三种展示逻辑是 ： 时间（lastLoveStartTime）加 已解除 判断逻辑 isInLove为false isInMarriage 也为false
 - 恋爱第四种展示逻辑是 ：isInLove 为false 并且lastLoveStartTime 为空

17.结婚
 - 结婚第一种展示逻辑是 ： isInMarriage 为true 展示结婚时间
 - 结婚第二种展示逻辑是 ：已解除 展示逻辑 isInMarriage 为false lastMarriageStartTime有值 展示时间+已解除
 - 结婚第三种展示逻辑是 ： //空待确认
 - 结婚第四种展示逻辑是 ：isInMarriage 为false 并且lastMarriageStartTime 为空

18. 跟进状态 
followUpStatus int[]  入参出参都一致 枚举写在前端 返回的也是int 需要转化

19. 操作（先待定，后续补充）


其中（当前进展、 当前进展停留时间、 推荐给会员时间、）需要支持排序






AI prompt增加
另外后续每一个小的步骤完毕后，都去更新设计文档src/views/matchmaker/MembershipFollowUp/DesignDoc.md, 同时更新src/views/matchmaker/MembershipFollowUp/Checklist.md并逐步执行，每一个小的步骤完毕后，更新 checklist 状态 ,不要生成多余的md文件。全部都放到 DesignDoc.md和Checklist.md里去