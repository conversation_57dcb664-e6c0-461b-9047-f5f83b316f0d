/**
 * @type {import('@qianshou/api2typescript').Config}
 */
const config = {
  requestFunctionFilePath: "packages/service/yapi/request.ts", // 请求函数文件路径
  apps: [
    {
      name: "qianshou-moderation-rest", // 节点服务名, 必须准确
      outputDir: "packages/service/qianshou-moderation-rest", // 输出目录
      paths: [
        '/v1/admins',
        '/v2/complaint/info',
        '/v2/complaint/create',
        '/v2/complaint/list',
        '/v2/complaint/update',
        '/v2/tencent/eSign/create-flow',
        '/v2/user/thumbs-up',
        '/v2/user/posts',
        '/v3/achievement',
        '/v3/admin/batch-entry',
        '/v3/admin/batch-entry-template',
        '/v3/ai/cold-reading',
        '/v3/ai-bot/conversation/list',
        '/v3/ai-bot/reply',
        '/v3/ai-image-processing-task',
        '/v3/ai-image-processing-tasks',
        '/v3/assessment/send-link',
        '/v3/assessment/send-report-link',
        '/v3/complaint/category',
        '/v3/complaint-review/update',
        '/v3/complaint-review/list',
        '/v3/complaint/update-deadline',
        '/v3/communication-quality-evaluation',
        '/v3/category-tag/search',
        '/v3/cc/record/url',
        '/v3/config/tts-token',
        '/v3/contract/reset-button',
        '/v3/collect/users',
        '/v3/customer/access-history',
        '/v3/config/rtc-token',
        '/v3/customer/phone',
        '/v3/customer/privacy',
        '/v3/daily-backlog/statistics',
        '/v3/daily-backlog/list',
        '/v3/daily-backlog',
        '/v3/exam-regulator/matchmaker',
        '/v3/leads-config/allocate',
        '/v3/leads-config/capacity',
        '/v3/lottery/info',
        '/v3/matchmaker/assignment/capacity/monitors',
        '/v3/matchmaker/assignment/future-availability',
        '/v3/matchmaker/assignment/future-availability/export',
        '/v3/matchmaker/assignment/capacity/statistics',
        '/v3/matchmaker/assignment/capacity/details',
        '/v3/matchmaker/assignment/capacity/details/export',
        '/v3/matchmaker/assignment/capacity/monitor',
        '/v3/matchmaker/assignment/new',
        '/v3/matchmaker/assignment/schedule',
        '/v3/matchmaker/assignment/schedule/export',
        '/v3/membership/statistic',
        '/v3/membership/follow-up/list',
        '/v3/membership/follow-up/status',
        '/v3/mm-recommend',
        '/v3/mm-recommend/alert',
        '/v3/mm-recommend/user-base',
        '/v3/order/bind',
        '/v3/order/unbind',
        '/v3/order/query-unbound/weCom',
        '/v3/order/reassign',
        '/v3/organization',
        '/v3/organization/tree',
        '/v3/passive-contact/note',
        '/v3/passive-contact',
        '/v3/passive-contacts',
        '/v3/passive-contact/notification',
        '/v3/passive-contacts/information',
        '/v3/passive-contact/we-chat',
        '/v3/passive-contact/save-note/alert',
        '/v3/performances',
        '/v3/performance/auto-allocate',
        '/v3/performance/order-confirms',
        '/v3/performance/order-confirms/export',
        '/v3/relationship-like/users',
        '/v3/regulator/we-com-sale-convert',
        '/v3/regulator/we-com-realtime-sales',
        '/v3/user/tags',
        '/v3/user-contacts',
        '/v3/user-feature-analysis/avatar-score',
        '/v3/salary',
        '/v3/satisfaction-record',
        '/v3/selection/list',
        '/v3/serve-regulator/meeting',
        '/v3/sop-exam/list',
        '/v3/sop-exam/timeout',
        '/v3/sop-training/ai-chat/build-trust-check',
        '/v3/sop-training/ai-chat/conversation',
        '/v3/sop-training/ai-chat/conversations',
        '/v3/sop-training/ai-chat/message',
        '/v3/sop-training/ai-chat/complete',
        '/v3/sop-training/ai-chat/messages',
        '/v3/sop-training/ai-chat/rankings',
        '/v3/sop-training/ai-chat/regulators',
        '/v3/sop-training/ai-chat/passive-info',
        '/v3/sop-training/build-trust/records',
        '/v3/sop-training/build-trust/process-results',
        '/v3/sop-training/ai-video-call/conversation/stop',
        '/v3/sop-training/ai-practice/conversation',
        '/v3/sop-training/ai-chat/node',
        '/v3/sop-training/ai-video-call/conversation',
        '/v3/sop-training/create/exam',
        '/v3/sop-training/mock-user-infos',
        '/v3/sop-training/manual',
        '/v3/sop-training/manual/list',
        '/v3/sop-training/telemarketing/examination',
        '/v3/sop-training/telemarketing/examination-feedback',
        '/v3/sop-training/telemarketing/examination/read',
        '/v3/sop-training/telemarketing/examinations',
        '/v3/sop-training/telemarketing/inspection',
        '/v3/sop-training/telemarketing/matchmakers',
        '/v3/sop-training/telemarketing/recognize',
        '/v3/sop-training/telemarketing/question',
        '/v3/sop-training/telemarketing/questions',
        '/v3/sop-training/standard-answers/recording',
        '/v3/sop-training/standard-answers/recordings',
        '/v3/sop-training/stage-objections',
        '/v3/sop-training/task/list',
        '/v3/sop-training/task/create',
        '/v3/sop-training/task',
        '/v3/sop-training/task-manage/stat',
        '/v3/sop-training/task-manage/detail',
        '/v3/sop-training/serve-mm/mate-standard/param',
        '/v3/sop-training/serve-mm/mate-standard/users',
        '/v3/sop-training/serve-mm/passive-contact/information',
        '/v3/sop-training/serve-mm/history-recommend-record',
        '/v3/sop-training/serve-mm/mm-recommend/user-base',
        '/v3/sop-training/serve-mm/mm-recommend/user-tags',
        '/v3/sop-training/serve-mm/recommend-introduction',
        '/v3/sop-training/serve-mm/mate-standard/user-profile',
        '/v3/sop-training/serve-mm/recommend',
        '/v3/sop-training/task/complete',
        '/v3/sop-training/task/status',
        '/v3/sop-training/task-manage/stat',
        '/v3/sop-training/task-manage/detail',
        '/v3/sop-training/auto-reply/inappropriate-messages',
        '/v3/sop-training/complaint-standard-answers/recordings',
        '/v3/sop-training/complaint-standard-answers/recording',
        '/v3/system-prompt',
        '/v3/system-prompts',
        '/v3/system-prompts/biz-type',
        '/v3/daily-backlog/serve-stage',
        '/v3/vacation-management/tasks',
        '/v3/we-com/ai-reply',
        '/v3/we-com/restore-call-log',
        '/v3/we-com/conversation/records',
        '/v3/whitelist',
      ]
    },
    {
      name: "qianshou-survey-rest", // 节点服务名, 必须准确
      outputDir: "packages/service/qianshou-survey-rest", // 输出目录
      paths: [
        '/v3/public-surveys/:id',
        '/v3/public-surveys/:id/answers'
      ]
    },
  ]
}

export default config
