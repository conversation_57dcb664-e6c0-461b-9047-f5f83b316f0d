<script setup lang="ts">
import { ref, defineEmits } from 'vue';
// 使用 a-tabs 组件，不需要显式导入
import SopPassiveStats from './SopPassiveStats.vue';
import SopPassiveTaskDetail from './SopPassiveTaskDetail.vue';
import SopPassiveDetail from '../SopExercise/SopPassive/SopPassiveDetail.vue';
import { Button } from '@qianshou/ui';

const emit = defineEmits(['close', 'refreshTaskDetail']);
const activeTab = ref('stats');
const showDetail = ref(false);
const conversationId = ref<number>(0);

const handleDetailClose = () => {
  showDetail.value = false;
  // 通知TaskDetail组件刷新数据
  if (activeTab.value === 'detail') {
    emit('refreshTaskDetail');
  }
};
</script>

<template>
  <div>
    <div v-show="!showDetail">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h4 style="margin: 0 10px 0 0; font-weight: bold;">牵线练习管理</h4>
        <Button type="link" size="small" @click="emit('close')" style="margin-right: 10px">
          返回培训系统
        </Button>
      </div>

      <a-tabs v-model:activeKey="activeTab" type="card">
        <a-tab-pane key="stats" tab="红娘统计">
          <SopPassiveStats />
        </a-tab-pane>

        <a-tab-pane key="detail" tab="任务明细">
          <SopPassiveTaskDetail @close="emit('close')"
            @view-detail="(id) => { conversationId = id; showDetail = true; }" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <div v-show="showDetail">
      <keep-alive :key="conversationId">
        <SopPassiveDetail v-if="conversationId" :conversationId="conversationId" @close="handleDetailClose" />
      </keep-alive>
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-tabs-nav) {
  margin-bottom: 0;
}
</style>
