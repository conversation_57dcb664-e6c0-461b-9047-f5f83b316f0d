<script setup lang="ts">
import { computed } from 'vue'
import { Drawer } from 'ant-design-vue'
import { useRecordings, useRecordingNavigation, useRecordingFilter } from '@/hooks/recordings'
import {
  RecordingHeader,
  RecordingPlayer,
  RecordingSummary,
  RecordingFilter,
  RecordingList
} from './components'

const emits = defineEmits(['close'])

// 销售录音库配置
const saleFilterConfig = {
  recordingType: 'sale' as const
}

// 使用配置化的录音数据hook
const {
  query,
  questions,
  currInd,
  currQues,
  record,
  currRecordTab,
  getQuestions,
  init,
  selectQuestion
} = useRecordings(saleFilterConfig)

const recordingRecords = computed(() => {
  // 如果 record 或 record.record 不存在，返回空数组
  if (!record?.value?.record) {
    return []
  }

  // 判断是否为标准录音库类型（有最外层的 text 属性）
  const isStandardRecording = 'text' in record.value && Array.isArray(record.value.text)

  return record.value.record.map((item: any, index: number) => {
    if (isStandardRecording) {
      // 标准录音库：从最外层的 text 数组获取文本内容，添加 callType
      return {
        url: item.url,
        callType: item.callType,
        text: (record.value as any)?.text?.[index] || ''
      }
    } else {
      // 客诉录音库：直接使用 record 中的 text，添加 callType
      return {
        url: item.url,
        text: item.text || '',
        callType: '' // 客诉录音库没有 callType，设为空字符串
      }
    }
  })
})

// 初始化
init()

// 使用录音导航hook
const {
  canNavigatePrev,
  canNavigateNext,
  navigatePrev,
  navigateNext
} = useRecordingNavigation(currInd, questions, selectQuestion)

// 使用配置化的录音筛选hook
const {
  visible,
  incomeOptions,
  genderOptions,
  openDrawer,
  closeDrawer,
  search
} = useRecordingFilter(query, getQuestions, saleFilterConfig)

// 计算当前录音标题
const recordingTitle = computed(() => `${currQues.value?.id ?? ''} ${currQues.value?.title ?? ''}`)

// 类型安全的收入选项 - 确保销售录音库使用正确的类型
const typedIncomeOptions = computed(() => incomeOptions as { label: string; value: number }[])


// 处理选择录音
const handleSelectQuestion = (index: number) => {
  closeDrawer()
  selectQuestion(index)
}

</script>
<template>
  <!-- 头部控制栏 -->
  <RecordingHeader :canNavigatePrev="canNavigatePrev" :canNavigateNext="canNavigateNext" @openDrawer="openDrawer"
    @navigatePrev="navigatePrev" @navigateNext="navigateNext" @close="emits('close')" />

  <div class="chat">
    <!-- 录音播放器 -->
    <RecordingPlayer :title="recordingTitle" :record="recordingRecords" :currRecordTab="currRecordTab"
      :recordingType="'sale'" @update:currRecordTab="currRecordTab = $event" />

    <!-- 录音总结 -->
    <RecordingSummary :summary="record?.sumUp" />
  </div>

  <!-- 录音筛选抽屉 -->
  <Drawer width="500" :zIndex="9996" placement="right" v-model:visible="visible">
    <template #title>
      <h3>录音库</h3>
    </template>

    <!-- 筛选条件 -->
    <RecordingFilter :query="query" :incomeOptions="typedIncomeOptions" :genderOptions="genderOptions"
      @search="search" />

    <!-- 录音列表 -->
    <RecordingList :questions="questions" :currInd="currInd" @select="handleSelectQuestion" />
  </Drawer>
</template>

<style scoped>
.chat {
  height: calc(100vh - 220px);
  margin-bottom: 20px;
  gap: 20px;
  display: flex;
  width: 100%;
  overflow: hidden;
}
</style>