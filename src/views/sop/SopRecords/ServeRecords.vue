<script setup lang="ts">
import { computed, ref } from 'vue'
import { Drawer } from 'ant-design-vue'
import { useRecordings, useRecordingNavigation, useRecordingFilter } from '@/hooks/recordings'
import { adminRights, GetV3SopTrainingComplaintStandardAnswersRecordingResponse } from '@qianshou/service'
import {
  RecordingHeader,
  RecordingPlayer,
  RecordingSummary,
  RecordingFilter,
  RecordingList,
  AddRecordingModal
} from './components'

const emits = defineEmits(['close'])

// 服务录音库配置
const serveFilterConfig = {
  recordingType: 'serve' as const
}

// 使用配置化的录音数据hook
const {
  query,
  questions,
  currInd,
  currQues,
  record,
  currRecordTab,
  getQuestions,
  init,
  selectQuestion
} = useRecordings(serveFilterConfig)

// 初始化
init()

// 使用录音导航hook
const {
  canNavigatePrev,
  canNavigateNext,
  navigatePrev,
  navigateNext
} = useRecordingNavigation(currInd, questions, selectQuestion)

// 使用配置化的录音筛选hook
const {
  visible,
  incomeOptions,
  genderOptions,
  openDrawer,
  closeDrawer,
  search,
  filterConfig
} = useRecordingFilter(query, getQuestions, serveFilterConfig)

// 类型守卫函数：检查 record 是否包含 detail 属性
function hasDetailProperty(record: any): record is GetV3SopTrainingComplaintStandardAnswersRecordingResponse['data'] {
  return record && 'detail' in record
}

// 计算当前录音标题
const recordingTitle = computed(() => `${currQues.value?.id ?? ''} ${currQues.value?.title ?? ''}`)

// 计算录音详情（备注）
const recordDetail = computed(() => {
  return record.value && hasDetailProperty(record.value) ? record.value.detail : undefined
})

// 类型安全的收入选项 - 确保服务录音库使用正确的类型
const typedIncomeOptions = computed(() => incomeOptions as { label: string; value: number }[])

// 新增录音弹窗状态
const addRecordingVisible = ref(false)

// 处理选择录音
const handleSelectQuestion = (index: number) => {
  closeDrawer()
  selectQuestion(index)
}

// 打开新增录音弹窗
const handleAddRecording = () => {
  addRecordingVisible.value = true
}

// 新增录音成功后的处理
const handleAddRecordingSuccess = async () => {
  addRecordingVisible.value = false
  await getQuestions() // 刷新录音列表
}

</script>
<template>
  <!-- 头部控制栏 -->
  <RecordingHeader :canNavigatePrev="canNavigatePrev" :canNavigateNext="canNavigateNext"
    :showAddRecording="!!adminRights?.isMatchmakerManager" @openDrawer="openDrawer" @navigatePrev="navigatePrev"
    @navigateNext="navigateNext" @close="emits('close')" @add-recording="handleAddRecording" />

  <div class="chat">
    <!-- 录音播放器 -->
    <RecordingPlayer :title="recordingTitle" :record="record?.record ?? []" :currRecordTab="currRecordTab"
      :recordingType="'serve'" @update:currRecordTab="currRecordTab = $event" />

    <!-- 录音总结 -->
    <RecordingSummary :summary="recordDetail" :title="'备注'" />
  </div>

  <!-- 录音筛选抽屉 -->
  <Drawer width="500" :zIndex="9996" placement="right" v-model:visible="visible">
    <template #title>
      <h3>录音库</h3>
    </template>

    <!-- 筛选条件 -->
    <RecordingFilter :query="query" :incomeOptions="typedIncomeOptions" :genderOptions="genderOptions"
      :filterConfig="filterConfig" @search="search" />

    <!-- 录音列表 -->
    <RecordingList :questions="questions" :currInd="currInd" @select="handleSelectQuestion" />
  </Drawer>

  <!-- 新增录音弹窗 -->
  <AddRecordingModal v-model:visible="addRecordingVisible" :filterConfig="filterConfig"
    @success="handleAddRecordingSuccess" />
</template>

<style scoped>
.chat {
  height: calc(100vh - 220px);
  margin-bottom: 20px;
  gap: 20px;
  display: flex;
  width: 100%;
  overflow: hidden;
}
</style>