<script setup lang="ts">
import { Button } from '@qianshou/ui'
import { MenuOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue'

defineProps<{
  canNavigatePrev: boolean
  canNavigateNext: boolean
  /** 是否显示新增录音按钮 */
  showAddRecording?: boolean
}>()

const emit = defineEmits<{
  (e: 'openDrawer'): void
  (e: 'navigatePrev'): void
  (e: 'navigateNext'): void
  (e: 'close'): void
  (e: 'add-recording'): void
}>()
</script>

<template>
  <div class="info">
    <div class="controls">
      <MenuOutlined class="icon" @click="emit('openDrawer')" />
      <span>录音库</span>
      <Button type="text" :disabled="!canNavigatePrev">
        <template #icon>
          <LeftOutlined class="icon" @click="emit('navigatePrev')" />
        </template>
      </Button>
      <Button type="text" :disabled="!canNavigateNext">
        <template #icon>
          <RightOutlined class="icon" @click="emit('navigateNext')" />
        </template>
      </Button>
      <Button v-if="showAddRecording" type="primary" @click="emit('add-recording')">新增录音</Button>
    </div>
    <a-alert style="flex:1" message="培训内容为内部资料, 严禁泄露" banner closable />
    <Button type="link" @click="emit('close')">返回培训系统</Button>
  </div>
</template>

<style scoped>
.info {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  box-sizing: border-box;
}

.controls {
  width: 400px;
  display: flex;
  gap: 10px;
  align-items: center;
  font-size: 16px;
}

.icon {
  font-size: 20px;
  cursor: pointer;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
