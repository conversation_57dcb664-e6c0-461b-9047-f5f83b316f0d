<script setup lang="ts">
defineProps<{
  questions: any[]
  currInd: number
}>()

const emit = defineEmits<{
  (e: 'select', index: number): void
}>()
</script>

<template>
  <div class="list">
    <div :class="['list-item', currInd === index ? 'active' : '']" v-for="(item, index) in questions" :key="item.id"
      @click="emit('select', index)">
      <span class="list-text">
        {{ `${item.id} ${item.title}` }}
      </span>
    </div>
  </div>
</template>

<style scoped>
.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  height: 36px;
  line-height: 36px;
  padding: 0 10px 0 10px;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
}

.list-item:hover {
  background: #fefefe;
  color: #6ecadf;
  opacity: 0.8;
}

.list-text {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.list-item:nth-child(odd) {
  background: #f0f0f0;
}

.list-item.active {
  color: #6ecadf
}
</style>
