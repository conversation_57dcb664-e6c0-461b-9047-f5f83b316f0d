<script setup lang="ts">
import { getPrivateFileUrl } from '@qianshou/service'
import { convertServeText, convertSaleText } from "@qianshou/common"

const props = defineProps<{
  title: string
  record: any
  currRecordTab: number
  recordingType?: 'sale' | 'serve'  // 录音类型：销售或服务
}>()

const emit = defineEmits<{
  (e: 'update:currRecordTab', value: number): void
}>()

// 根据录音类型决定是否处理 URL
const getAudioUrl = (url: string) => {
  // 销售录音需要处理 URL，服务录音直接使用
  return props.recordingType === 'serve' ? url : getPrivateFileUrl(url)
}

const convertText = (text: string, callType: string) => {
  return props.recordingType === 'serve' ? convertServeText(text, callType === 'phone' ? '0' : '1') : convertSaleText(text, callType === 'phone' ? '0' : '1')
}
</script>

<template>
  <a-card bordered :headStyle="{ background: '#f0f0f0' }" size="small" class="left-card chat-card" title="录音库">
    <div class="card-content">
      <h2>{{ title }}</h2>

      <a-tabs type="card" :activeKey="currRecordTab" :destroyInactiveTabPane="true"
        @change="(key) => emit('update:currRecordTab', key)">
        <a-tab-pane v-for="(item, index) in record" :tab="`录音${index + 1}`" :key="index">
          <div class="tab-content">
            <audio :ref="`record${index}`" controls class="audio" v-if="item" :src="getAudioUrl(item.url)"
              controlslist="nodownload">
            </audio>
            <div class="content-wrapper">
              <div class="content" v-html="convertText(item?.text, item?.callType)">
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>
</template>

<style scoped>
.chat-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-card {
  flex: 7;
  height: 100%;
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.content-wrapper {
  flex: 1;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
}

.content {
  height: 100%;
  background-color: #404040;
  font-size: 17px;
  overflow-y: auto;
  padding: 10px;
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  max-height: calc(100vh - 250px);
}

.audio {
  width: 100%;
  height: 40px;
}
</style>
