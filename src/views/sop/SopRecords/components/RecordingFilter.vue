<script setup lang="ts">
import { Button } from '@qianshou/ui'
import { Select } from 'ant-design-vue'
import { config } from '@qianshou/service'
import { ExtendedRecordingQuery, FilterConfig } from '@/hooks/recordings/useRecordingFilter'

const query = defineModel<ExtendedRecordingQuery>('query', { required: true })

defineProps<{
  incomeOptions: { label: string; value: number }[]
  genderOptions: { label: string; value: string }[]
  /** 筛选配置，用于控制显示哪些筛选选项 */
  filterConfig?: FilterConfig
}>()

const emit = defineEmits<{
  (e: 'search'): void
}>()
</script>

<template>
  <div class="filter">
    <Select v-model:value="query.source" mode="multiple" style="width: 180px" placeholder="渠道" size="small" allowClear
      :max-tag-count="1" :options="config?.selectionEnum?.standardAnswerSource">
    </Select>
    <!-- 人群筛选 - 仅在销售录音库中显示 -->
    <Select v-if="(filterConfig?.recordingType ?? 'sale') === 'sale'" v-model:value="query.characteristic"
      mode="multiple" style="width: 220px" placeholder="人群" size="small" allowClear :max-tag-count="1"
      :options="config?.selectionEnum?.standardAnswerCharacteristic">
    </Select>
    <!-- 一级分类筛选 - 仅在服务录音库中显示 -->
    <Select v-if="filterConfig?.recordingType === 'serve'" v-model:value="query.category" mode="multiple"
      style="width: 180px" placeholder="一级分类" size="small" allowClear :max-tag-count="1"
      :options="config?.selectionEnum?.sopTrainingComplaintStandardAnswerCategory">
    </Select>
    <Select v-model:value="query.gender" mode="multiple" style="width: 180px" placeholder="性别" size="small" allowClear
      :max-tag-count="1" :options="genderOptions">
    </Select>
    <Select v-model:value="query.income" mode="multiple" style="width: 180px" placeholder="收入" size="small" allowClear
      :max-tag-count="1" :options="incomeOptions">
    </Select>
    <Button @click="emit('search')">搜索</Button>
  </div>
</template>

<style scoped>
.filter {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}
</style>
