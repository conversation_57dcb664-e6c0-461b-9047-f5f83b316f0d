<script setup lang="ts">
defineProps<{
  summary?: string
  title?: string
}>()
</script>

<template>
  <a-card class="right-card chat-card" bordered :headStyle="{ background: '#f0f0f0' }" size="small"
    :title="title ?? '总结'">
    <div class="card-content">
      <div class="sum-wrapper">
        <pre class="sum">{{ summary }}</pre>
      </div>
    </div>
  </a-card>
</template>

<style scoped>
.chat-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.right-card {
  flex: 3;
  height: 100%;
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sum-wrapper {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sum {
  height: 100%;
  overflow-y: auto;
  margin: 0;
  min-height: 0;
  max-height: calc(100vh - 250px);
  white-space: pre-wrap;
}
</style>
