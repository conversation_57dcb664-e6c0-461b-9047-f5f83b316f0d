<script setup lang="ts">
import { ref, computed } from 'vue'
import { Modal, Form, FormItem, Select, Input, message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { config, postV3SopTrainingComplaintStandardAnswersRecording, PostV3SopTrainingComplaintStandardAnswersRecordingRequest } from '@qianshou/service'
import { FilterConfig } from '@/hooks/recordings/useRecordingFilter'

interface Props {
  visible: boolean
  /** 筛选配置，用于控制显示哪些表单字段 */
  filterConfig?: FilterConfig
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据类型，基于 API 请求类型
type FormData = PostV3SopTrainingComplaintStandardAnswersRecordingRequest & {
  recordIDs: string[]  // 确保 recordIDs 是字符串数组
  userID?: string | number
}

// 表单数据
const formData = ref<FormData>({
  detail: '',
  userID: undefined,
  category: '',
  recordIDs: []
})

// 表单引用
const formRef = ref()

// 提交状态
const loading = ref(false)

// 类型守卫函数：检查 userID 是否为有效字符串
function isStringUserID(userID: string | number | undefined): userID is string {
  return typeof userID === 'string' && userID.trim().length > 0
}

// 表单验证规则 - 使用计算属性确保响应式更新
const rules = computed(() => ({
  recordIDs: [
    { required: true, message: '请输入至少一个通话ID', trigger: 'change' }
  ] as Rule[],
  category: [
    { required: true, message: '请选择投诉一级分类', trigger: 'change' }
  ] as Rule[]
}))

// 关闭弹窗
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  formData.value = {
    detail: '',
    userID: undefined,
    category: '',
    recordIDs: []
  }
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    loading.value = true

    // 安全处理 userID 类型转换
    const processedUserID = isStringUserID(formData.value.userID)
      ? Number(formData.value.userID)
      : typeof formData.value.userID === 'number'
        ? formData.value.userID
        : undefined

    // 调用 API
    await postV3SopTrainingComplaintStandardAnswersRecording({
      detail: formData.value.detail,
      userID: processedUserID,
      category: formData.value.category,
      recordIDs: formData.value.recordIDs
    })

    message.success('录音添加成功')
    emit('success')
    handleCancel()

  } catch (error) {
    console.error('新增录音失败:', error)
    message.error('新增录音失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <Modal :visible="visible" title="新增录音" width="600px" :confirmLoading="loading" @ok="handleSubmit"
    @cancel="handleCancel">
    <Form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <!-- 用户ID -->
      <FormItem label="用户ID" name="userID" required>
        <Input v-model:value="formData.userID" placeholder="请输入用户ID" />
      </FormItem>

      <!-- 通话ID -->
      <FormItem label="通话ID" name="recordIDs" required>
        <Select v-model:value="formData.recordIDs" mode="tags" placeholder="请输入通话ID，支持输入多个" style="width: 100%"
          :tokenSeparators="[',', ' ']" />
      </FormItem>

      <!-- 投诉一级分类 - 仅在服务录音库中显示 -->
      <FormItem label="投诉一级分类" name="category" required>
        <Select v-model:value="formData.category" placeholder="请选择投诉一级分类" style="width: 100%"
          :options="config?.selectionEnum?.sopTrainingComplaintStandardAnswerCategory" />
      </FormItem>

      <!-- 备注 -->
      <FormItem label="备注" name="detail">
        <Input.TextArea v-model:value="formData.detail" placeholder="请输入备注信息" :rows="4" :maxlength="500" showCount />
      </FormItem>
    </Form>
  </Modal>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
