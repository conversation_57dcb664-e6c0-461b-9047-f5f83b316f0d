<script setup lang="ts">
import { ref, defineEmits, shallowRef, computed } from 'vue';
// 使用 a-tabs 组件，不需要显式导入
import SopServeStats from './SopServeStats.vue';
import SopServeTaskDetail from './SopServeTaskDetail.vue';
import SopServeDetail from '../SopExercise/SopServe/SopServeDetail.vue';
import SopServeDetailCall from '../SopExercise/SopServe/SopServeDetailCall.vue';
import MemberToPassive from '../SopExercise/SopRecommend/SopRecommend.vue';
import PassiveToMember from '../SopExercise/SopServe/PassiveToMember.vue';
import SopObjectionDetail from '../SopExercise/SopServe/SopObjectionDetail.vue';
import { Button } from '@qianshou/ui';

const emit = defineEmits(['close', 'refreshTaskDetail']);
const activeTab = ref('stats');
const showDetail = ref(false);
const conversationId = ref<number>(0);
const taskId = ref<number>();
const taskStatus = ref<number>();
const questionId = ref<number>();

// 动态组件相关
const currentComponent = shallowRef();

const handleDetailClose = () => {
  showDetail.value = false;
  currentComponent.value = undefined;
  // 通知TaskDetail组件刷新数据
  if (activeTab.value === 'detail') {
    emit('refreshTaskDetail');
  }
};

// 处理不同类型的任务详情页
const handleViewDetailWithType = (data: { conversationId: number, todoType?: string, taskId?: number, taskStatus?: number; questionId?: number }) => {
  conversationId.value = data.conversationId;
  taskId.value = data.taskId;
  taskStatus.value = data.taskStatus;
  questionId.value = data.questionId;

  if (data.todoType === 'firstCall') {
    currentComponent.value = SopServeDetailCall;
  } else if (data.todoType === 'start') {
    currentComponent.value = SopServeDetail;
  } else if (data.todoType === 'recommendMemberToPassive') {
    currentComponent.value = MemberToPassive;
  } else if (data.todoType === 'recommendPassiveToMember') {
    currentComponent.value = PassiveToMember;
  } else if (data.todoType === 'objectionHandling') {
    currentComponent.value = SopObjectionDetail;
  } else {
    // 默认使用 SopServeDetail
    currentComponent.value = SopServeDetail;
  }

  showDetail.value = true;
};

const componentProps = computed(() => ({
  conversationId: conversationId.value,
  taskId: taskId.value,
  taskStatus: taskStatus.value,
  questionId: questionId.value,
  // 从管理页面进入的详情页，所有按钮都应该禁用
  isFromManage: true
}));
</script>

<template>
  <div>
    <div v-show="!showDetail">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h4 style="margin: 0 10px 0 0; font-weight: bold;">服务练习管理</h4>
        <Button type="link" size="small" @click="emit('close')" style="margin-right: 10px">
          返回培训系统
        </Button>
      </div>

      <a-tabs v-model:activeKey="activeTab" type="card">
        <a-tab-pane key="stats" tab="红娘统计">
          <SopServeStats />
        </a-tab-pane>

        <a-tab-pane key="detail" tab="任务明细">
          <SopServeTaskDetail @close="emit('close')"
            @view-detail="(id) => { conversationId = id; showDetail = true; currentComponent.value = SopServeDetail; }"
            @view-detail-with-type="handleViewDetailWithType" />
        </a-tab-pane>
      </a-tabs>
    </div>

    <component v-show="showDetail" :is="currentComponent" v-bind="componentProps" @close="handleDetailClose" />
  </div>
</template>

<style scoped>
:deep(.ant-tabs-nav) {
  margin-bottom: 0;
}
</style>
