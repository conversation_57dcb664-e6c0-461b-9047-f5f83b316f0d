<script setup lang="ts">
import { ref, reactive, defineEmits, onMounted, h } from 'vue';
import { TableColumnType, Space, Tag, Button as AButton, Select } from 'ant-design-vue';
import { Button, DateTimeRangePicker } from '@qianshou/ui'
import { OrganizationCascader } from '@qianshou/component';
import dayjs, { type Dayjs } from 'dayjs';
import {
  adminRights,
  getV3SopTrainingTaskManageDetail,
  type GetV3SopTrainingTaskManageDetailRequest,
  type GetV3SopTrainingTaskManageDetailResponse
} from '@qianshou/service';

const emit = defineEmits(['close', 'view-detail', 'view-detail-with-type']);

// 任务明细查询参数
const detailQuery = reactive<GetV3SopTrainingTaskManageDetailRequest & { adminId?: number, todoType?: string }>({
  taskType: 'serveMatchmaker',
  orgId: undefined,
  startTime: dayjs().subtract(30, 'day').valueOf() * 1000000, // 转换为纳秒
  endTime: dayjs().valueOf() * 1000000, // 转换为纳秒
  status: undefined,
  adminId: undefined
});

// 数据源
const dataSource = ref<GetV3SopTrainingTaskManageDetailResponse['data']>();

// 加载状态
const loading = ref(false);

// 日期范围
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()]);

// 总条数
const total = ref(0);

// 状态选项
const statusOptions = [
  { label: '待办', value: '0' },
  { label: '完成', value: '1' },
  { label: '失败', value: '2' }
];

// 任务类型选项
const todoTypeOptions = [
  { label: '启动', value: 'start' },
  { label: '首通', value: 'firstCall' },
  { label: '把会员介绍给被动方', value: 'recommendMemberToPassive' },
  { label: '把被动方推荐给会员', value: 'recommendPassiveToMember' },
  { label: '异议处理', value: 'objectionHandling' }
];

// 任务明细表格列定义
const columns: TableColumnType<NonNullable<NonNullable<GetV3SopTrainingTaskManageDetailResponse['data']>['list']>[0]>[] = [
  {
    title: 'TaskID',
    key: 'id',
    dataIndex: 'id',
    align: 'center'
  },
  {
    title: '红娘',
    key: 'adminName',
    dataIndex: 'adminName',
    align: 'center'
  },
  {
    title: '主管',
    key: 'adminLeaderName',
    dataIndex: 'adminLeaderName',
    align: 'center'
  },
  {
    title: '待办类型',
    key: 'todoType',
    dataIndex: 'todoType',
    align: 'center',
    customRender: ({ text }) => {
      const typeMap: Record<string, string> = {
        'start': '启动',
        'firstCall': '首通',
        'recommendMemberToPassive': '推荐会员',
        'recommendPassiveToMember': '推荐被动方',
        'objectionHandling': '异议处理'
      };
      return typeMap[text as string] || text;
    }
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    align: 'center',
    customRender: ({ text }) => {
      const statusMap: Record<number, { text: string; color: string }> = {
        0: { text: '待完成', color: 'orange' },
        1: { text: '完成', color: 'green' },
        2: { text: '失败', color: 'red' }
      };
      const status = statusMap[text as number] || { text: '未知', color: 'default' };
      return h(Tag, { color: status.color }, () => status.text);
    }
  },
  {
    title: '状态备注',
    key: 'completedResult',
    dataIndex: 'completedResult',
    width: 200,
    align: 'center',
    customRender: ({ text }) => {
      if (!text) return '';
      // 将\n替换为<br>标签
      const formattedText = String(text).replace(/\n/g, '<br>');
      return h('div', {
        style: { maxWidth: '200px', overflow: 'auto' },
        innerHTML: formattedText
      });
    }
  },
  {
    title: '操作',
    key: 'operation',
    align: 'center',
    fixed: 'right' as const,
    customRender: ({ record }: { record: NonNullable<NonNullable<GetV3SopTrainingTaskManageDetailResponse['data']>['list']>[0] }) => {
      return adminRights?.allHigherRight || adminRights?.isDeveloperOrAdmin ? h(AButton, {
        type: 'link',
        size: 'small',
        style: { padding: 0 },
        onClick: () => {
          // 根据任务类型发送不同的事件
          emit('view-detail-with-type', {
            conversationId: record.conversationId ?? 0,
            todoType: record.todoType,
            taskId: record.id,
            taskStatus: record.status,
            questionId: record.extra?.questionId
          });
        }
      }, () => '查看') : '';
    }
  }
];

// 获取任务明细数据
const fetchData = async () => {
  loading.value = true;
  try {
    // 更新时间范围
    if (dateRange.value) {
      detailQuery.startTime = dateRange.value[0].valueOf() * 1000000; // 转换为纳秒
      detailQuery.endTime = dateRange.value[1].valueOf() * 1000000; // 转换为纳秒
    }

    const response = await getV3SopTrainingTaskManageDetail(detailQuery);
    if (response.data) {
      dataSource.value = response.data;
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取任务明细数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const search = () => {
  fetchData();
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<template>
  <div>
    <Space wrap style="margin-bottom: 16px; margin-top: 16px;">
      <OrganizationCascader size="small" v-model="detailQuery.orgId" placeholder="按组织架构" clearable
        style="min-width: 200px" />
      <DateTimeRangePicker v-model="dateRange" :placeholder="['任务时间', '任务时间']" />
      <Select size="small" v-model:value="detailQuery.todoType" placeholder="按任务类型筛选" allowClear
        style="min-width: 160px" :options="todoTypeOptions" />
      <Select size="small" v-model:value="detailQuery.status" columns placeholder="按状态筛选" mode="multiple"
        style="min-width: 160px" :options="statusOptions" />
      <Button type="primary" size="small" @click="search">搜索</Button>
    </Space>

    <a-table class="table" size='small' :columns="columns" :data-source="dataSource?.list" :loading="loading"
      :pagination="{
        pageSize: 50,
        total,
        showSizeChanger: true,
        showQuickJumper: true,
        size: 'small',
        showTotal: (total: number, range: [number, number]) => `总共 ${total}条 ${range[0]}-${range[1]}`
      }" rowKey="id" />
  </div>
</template>

<style scoped>
.table {
  margin-top: 16px;
}

:deep(.ant-table-thead > tr > th) {
  text-align: center;
  padding: 8px 4px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 4px;
}
</style>
