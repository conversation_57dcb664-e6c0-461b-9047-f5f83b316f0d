<script setup lang="ts">
import { ref } from 'vue';
import SopServeList from '../SopExercise/SopServe/SopServeList.vue';
import SopServeManage from './SopServeManage.vue';
import ServeRecords from '../SopRecords/ServeRecords.vue';

const isStart = ref(false);
const type = ref('');

const cardOptions = [
  { label: '服务练习', value: 'practice', background: 'linear-gradient(145deg, #FF58E4 0%, #69BAF9 100%)' },
  { label: '服务练习管理', value: 'manage', background: 'linear-gradient(145deg, #91D3A0 0%, #65C7DD 100%)' },
  { label: '录音库', value: 'serve-records', background: 'linear-gradient(145deg, #058BB6 0%, #10F1FF 100%)' }
];

const choose = (val: string) => {
  type.value = val;
  isStart.value = true;
};
</script>

<template>
  <div class="app-container">
    <template v-if="!isStart">
      <h3>服务培训系统</h3>
      <div class="opts type-opt">
        <template v-for="item in cardOptions" :key="item.value">
          <div class="card" :style="{ background: item.background }" @click="choose(item.value)">
            {{ item.label }}
          </div>
        </template>
      </div>
    </template>
    <template v-else>
      <SopServeList @close="isStart = false" v-if="isStart && type === 'practice'" />
      <SopServeManage @close="isStart = false" v-if="isStart && type === 'manage'" />
      <ServeRecords @close="isStart = false" v-if="isStart && type === 'serve-records'" />
    </template>
  </div>
</template>

<style scoped>
.app-container {
  position: relative;
  padding: 10px;
}

.opts {
  width: 100%;
  display: flex;
  gap: 16px;
  flex: 1;
}

.type-opt {
  margin-bottom: 50px;
}

.card {
  width: 260px;
  height: 150px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.card:hover {
  opacity: 0.7;
}
</style>
