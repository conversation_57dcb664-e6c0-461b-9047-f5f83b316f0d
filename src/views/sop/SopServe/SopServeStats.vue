<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { TableColumnType, Space, Select } from 'ant-design-vue';
import type { TablePaginationConfig } from 'ant-design-vue';
import type { SorterResult } from 'ant-design-vue/es/table/interface';
import { Button, DateTimeRangePicker } from '@qianshou/ui'
import { OrganizationCascader } from '@qianshou/component';
import dayjs, { type Dayjs } from 'dayjs';
import {
  getV3SopTrainingTaskManageStat,
  type GetV3SopTrainingTaskManageStatRequest,
  type GetV3SopTrainingTaskManageStatResponse
} from '@qianshou/service';

// 红娘统计查询参数
const statsQuery = reactive<GetV3SopTrainingTaskManageStatRequest & { adminId?: number }>({
  taskType: 'serveMatchmaker',
  orgId: undefined,
  startTime: dayjs().subtract(30, 'day').valueOf() * 1000000, // 转换为纳秒
  endTime: dayjs().valueOf() * 1000000, // 转换为纳秒
  status: undefined,
  adminId: undefined,
  orderBy: undefined,
  orderType: undefined
});

// 总条数
const total = ref(0);

// 数据源
const dataSource = ref<GetV3SopTrainingTaskManageStatResponse['data']>([]);

// 加载状态
const loading = ref(false);

// 日期范围
const dateRange = ref<[Dayjs, Dayjs]>([dayjs().subtract(30, 'day'), dayjs()]);


// 任务类型选项
const todoTypeOptions = [
  { label: '启动', value: 'start' },
  { label: '首通', value: 'firstCall' },
  { label: '把会员介绍给被动方', value: 'recommendMemberToPassive' },
  { label: '把被动方推荐给会员', value: 'recommendPassiveToMember' },
  { label: '异议处理', value: 'objectionHandling' }
];

// 红娘统计表格列定义
const columns: TableColumnType<NonNullable<GetV3SopTrainingTaskManageStatResponse['data']>[0]>[] = [
  {
    title: '红娘',
    key: 'adminName',
    dataIndex: 'adminName',
    align: 'center',
    customRender: ({ record }) => `${record.adminName || ''}(${record.adminId || ''})`
  },
  {
    title: '入职时长（天数）',
    key: 'adminEntryTime',
    dataIndex: 'adminEntryTime',
    align: 'center',
    customRender: ({ record }) => {
      if (!record.adminEntryTime) return '-';
      const entryTime = dayjs(record.adminEntryTime / 1000000); // 纳秒转毫秒
      const days = dayjs().diff(entryTime, 'day');
      return days;
    }
  },
  {
    title: '任务量',
    key: 'total',
    dataIndex: ['taskStat', 'total'],
    align: 'center',
    sorter: true
  },
  {
    title: '完成量',
    key: 'completed',
    dataIndex: ['taskStat', 'completed'],
    align: 'center',
    sorter: true,
    customRender: ({ record }) => record.taskStat?.completed || 0
  },
  {
    title: '完成率',
    key: 'completedRate',
    align: 'center',
    sorter: true,
    customRender: ({ record }) => {
      const rate = record.taskStat?.completedRate;
      return rate !== undefined ? `${(rate * 100).toFixed(2)}%` : '0%';
    }
  },
  {
    title: '成功量',
    key: 'succeed',
    dataIndex: ['taskStat', 'succeed'],
    align: 'center',
    sorter: true,
    customRender: ({ record }) => record.taskStat?.succeed || 0
  },
  {
    title: '成功率',
    key: 'succeedRate',
    align: 'center',
    sorter: true,
    customRender: ({ record }) => {
      const rate = record.taskStat?.succeedRate;
      return rate !== undefined ? `${(rate * 100).toFixed(2)}%` : '0%';
    }
  }
];

// 获取红娘统计数据
const fetchData = async () => {
  loading.value = true;
  try {
    // 更新时间范围
    if (dateRange.value) {
      statsQuery.startTime = dateRange.value[0].valueOf() * 1000000; // 转换为纳秒
      statsQuery.endTime = dateRange.value[1].valueOf() * 1000000; // 转换为纳秒
    }

    const response = await getV3SopTrainingTaskManageStat(statsQuery);
    if (response.data) {
      dataSource.value = response.data;
      total.value = response.data.length || 0;
    }
  } catch (error) {
    console.error('获取红娘统计数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理表格变化事件（排序、分页等）
const handleTableChange = (_pagination: TablePaginationConfig, _filters: Record<string, any>, sorter: SorterResult<NonNullable<GetV3SopTrainingTaskManageStatResponse['data']>[0]>) => {
  // 处理排序
  if (sorter && sorter.columnKey) {
    statsQuery.orderBy = sorter.columnKey as string;
    statsQuery.orderType = sorter.order === 'ascend' ? 'asc' : sorter.order === 'descend' ? 'desc' : undefined;
  } else {
    statsQuery.orderBy = undefined;
    statsQuery.orderType = undefined;
  }

  fetchData();
};

// 搜索
const search = () => {
  fetchData();
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<template>
  <Space wrap style="margin-bottom: 16px; margin-top: 16px;">

    <Select size="small" v-model:value="statsQuery.todoType" placeholder="按任务类型筛选" allowClear style="min-width: 160px"
      :options="todoTypeOptions" />
    <OrganizationCascader size="small" v-model="statsQuery.orgId" placeholder="按组织架构" clearable
      style="min-width: 200px" />
    <DateTimeRangePicker v-model="dateRange" :placeholder="['任务时间', '任务时间']" />
    <Button type="primary" size="small" @click="search">搜索</Button>
  </Space>

  <a-table class="table" size='small' :columns="columns" :data-source="dataSource" :loading="loading"
    @change="handleTableChange" :pagination="{
      pageSize: 50,
      total,
      showSizeChanger: true,
      showQuickJumper: true,
      size: 'small',
      showTotal: (total: number, range: [number, number]) => `总共 ${total}条 ${range[0]}-${range[1]}`
    }" rowKey="adminId" />
</template>

<style scoped>
.table {
  margin-top: 16px;
}

:deep(.ant-table-thead > tr > th) {
  text-align: center;
  padding: 8px 4px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 4px;
}
</style>
