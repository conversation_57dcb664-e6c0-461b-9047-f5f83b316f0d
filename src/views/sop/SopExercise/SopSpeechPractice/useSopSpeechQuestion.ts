import { ref, type Ref, type Injection<PERSON><PERSON>, provide, inject } from 'vue'
import { api } from '@qianshou/service'
import type { RequiredAwaitedReturnType } from '@qianshou/common'
import type { GetV3SopTrainingTelemarketingQuestionResponse } from '@qianshou/service'
import { useBaseQuestion, type QuestionCon } from '../composables/useBaseQuestion'

// 类型定义优化
type ApiResponse<T extends (...args: any) => any> = RequiredAwaitedReturnType<T>
export type QuestionList = Required<ApiResponse<typeof api.getV3SopTrainingTelemarketingQuestions>['data']>['list']
export type Question = QuestionList[number]
export type AnswerRecord = Required<Required<GetV3SopTrainingTelemarketingQuestionResponse>['data']>['answerRecords'][number]

export const questionsKey: InjectionKey<{
  query: Ref<Query>;
  questions: Ref<Question[]>;
  currentQuestion: Ref<QuestionCon | undefined>
  choose: (item: QuestionCon) => void
  answerRecords: Ref<AnswerRecord[] | undefined>
  refreshQuestion: (item?: QuestionCon) => Promise<void>
  refreshQuestions: () => Promise<void>
}> = Symbol("题目列表");



type Query = {
  difficulty: string[] | undefined
  category: string[] | undefined
}


export const useSopSpeechQuestion = (destroy: () => void) => {
  const questions = ref<Question[]>([])
  const answerRecords = ref<AnswerRecord[]>()

  const query = ref<Query>({
    difficulty: undefined,
    category: undefined
  })

  const refreshQuestions = async () => {
    try {
      const res = await api.getV3SopTrainingTelemarketingQuestions({
        difficulty: query.value.difficulty,
        category: query.value.category
      })
      questions.value = res?.data?.list ?? []
    } catch (error) {
      console.error('获取题目列表失败:', error)
      questions.value = []
    }
  }

  const refreshQuestion = async (item?: QuestionCon) => {
    const questionID = item?.id ?? currentQuestion.value?.id
    if (typeof questionID !== 'number') return

    try {
      const { data } = await api.getV3SopTrainingTelemarketingQuestion({ questionID })
      currentQuestion.value = data?.question
      answerRecords.value = data?.answerRecords
    } catch (error) {
      console.error('获取题目详情失败:', error)
    }
  }

  const {
    currentQuestion,
    result,
    loading,
    sendMessage,
    resultText,
    startASR,
    stopASR,
  } = useBaseQuestion({
    onQuestionUpdate: async () => {
      await Promise.all([refreshQuestions(), refreshQuestion()])
    }
  })

  const choose = async (item: QuestionCon) => {
    if (!item?.id || currentQuestion.value?.id === item?.id) return

    await refreshQuestion(item)

    result.value = undefined
    destroy()
  }

  const init = async () => {
    await refreshQuestions()
    if (questions.value?.[0]) {
      await choose(questions.value[0])
    }
  }

  init()

  provide(questionsKey, {
    query,
    questions,
    currentQuestion,
    choose,
    answerRecords,
    refreshQuestion,
    refreshQuestions,
  })

  return {
    query,
    questions,
    currentQuestion,
    choose,
    answerRecords,
    refreshQuestion,
    refreshQuestions,
    loading,
    result,
    sendMessage,
    resultText,
    startASR,
    stopASR,
  }
}

export const useInjectQuestions = () => {
  const questions = inject(questionsKey)
  if (!questions) {
    throw new Error('useInjectQuestions must be used within a provider')
  }
  return questions
} 