import { computed, type Ref, type ComputedRef, type InjectionKey, provide, inject } from 'vue'
import { getRandomElements } from '@qianshou/common'
import type { GetV3SopTrainingTelemarketingQuestionResponse, GetV3SopTrainingTelemarketingQuestionsResponse } from '@qianshou/service'


type Question = Required<Required<GetV3SopTrainingTelemarketingQuestionsResponse>['data']>['list'][number]
export type QuestionCon = Required<Required<GetV3SopTrainingTelemarketingQuestionResponse>['data']>['question']

export const controlsKey: InjectionKey<{
  prev: () => void;
  next: () => void;
  random: () => void;
  currInd: ComputedRef<number>
}> = Symbol("题目操作");

export const useSopSpeechControl = (
  questions: Ref<Question[]>,
  currentQuestion: Ref<QuestionCon | undefined>,
  choose: (item: QuestionCon) => Promise<void>,
  refreshQuestions: () => Promise<void>
) => {
  const currInd = computed(() => questions.value?.findIndex(item => item.id === currentQuestion.value?.id) ?? 0)

  const prev = async () => {
    if (!questions.value?.[currInd.value - 1]) return;
    await choose(questions.value?.[currInd.value - 1])
    await refreshQuestions()
  }

  const next = async () => {
    if (!questions.value?.[currInd.value + 1]) return
    await choose(questions.value?.[currInd.value + 1])
    await refreshQuestions()
  }

  const random = async () => {
    if (!questions.value) return
    const arrs = questions.value?.map(x => String(x.id))
    const ind = getRandomElements(arrs, 1)?.[0]
    const item = questions.value.find(x => x.id === Number(ind))
    if (!item) return
    await choose(item)
    await refreshQuestions()
  }


  provide(controlsKey, {
    prev,
    next,
    random,
    currInd,
  })

  return {
    prev,
    next,
    random,
    currInd,
  }
}



export const useInjectControls = () => {
  const controls = inject(controlsKey)
  if (!controls) {
    throw new Error('useInjectControls must be used within a provider')
  }
  return controls
}