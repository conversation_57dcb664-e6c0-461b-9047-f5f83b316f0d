<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { Table } from 'ant-design-vue';
import { getV3SopTrainingServeMmHistoryRecommendRecord } from "@qianshou/service";
import { getIncomeLabel, type IncomeMinEnumsType, type IncomeMaxEnumsType } from "@qianshou/component";
import InfoPopover from '@/views/customer/components/TablesCardComps/MateRecommendList/InfoPopover.vue';

const props = defineProps<{
  taskId?: number
}>();

// 历史记录数据
const historyRecords = ref<any[]>([]);
const historyLoading = ref(false);
const historyTotal = ref(0);

// 获取历史记录数据
const fetchHistoryRecords = async () => {
  if (!props.taskId) return;
  historyLoading.value = true;
  try {
    const { data } = await getV3SopTrainingServeMmHistoryRecommendRecord({
      taskId: props.taskId
    });
    historyRecords.value = data?.records || [];
    historyTotal.value = data?.total || 0;
  } catch (error) {
    console.error('获取历史记录失败:', error);
  } finally {
    historyLoading.value = false;
  }
};

// 历史记录表格列定义
const historyColumns: any = [
  {
    title: '头像',
    dataIndex: 'avatar',
    key: 'avatar',
    width: 80,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 80,
    fixed: 'left'
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 60,
    align: 'center',
  },
  {
    title: '身高',
    dataIndex: 'height',
    key: 'height',
    width: 60,
    align: 'center',
  },
  {
    title: '学历',
    dataIndex: 'education',
    key: 'education',
    width: 80,
  },
  {
    title: '年收入',
    dataIndex: 'income',
    key: 'income',
    width: 80,
    customRender: ({ record }) => {
      if (record.incomeRangeMin === undefined || record.incomeRangeMax === undefined) return '-';
      return getIncomeLabel(
        record.incomeRangeMin as IncomeMinEnumsType,
        record.incomeRangeMax as IncomeMaxEnumsType
      );
    }
  },
  {
    title: '婚况',
    dataIndex: 'maritalStatus',
    key: 'maritalStatus',
    width: 70,
  },
  {
    title: '推荐语',
    dataIndex: 'recommendReason',
    key: 'recommendReason',
    width: 120,
  },
  {
    title: '结果',
    dataIndex: 'recommendResult',
    key: 'recommendResult',
    width: 70,
    align: 'center',
    customRender: ({ record }) => {
      return record.recommendResult ? '通过' : '不通过';
    }
  },
  {
    title: '原因',
    dataIndex: 'failReason',
    key: 'failReason',
    width: 120,
  }
];

// 监听taskId变化，重新获取数据
watch(() => props.taskId, (newVal) => {
  if (newVal) {
    fetchHistoryRecords();
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  if (props.taskId) {
    fetchHistoryRecords();
  }
});
</script>

<template>
  <div class="history-table-wrapper">
    <Table bordered size="small" rowKey="id" :columns="historyColumns" :dataSource="historyRecords"
      :loading="historyLoading" :scroll="{ x: '800px' }" :pagination="{
        pageSize: 50,
        total: historyTotal,
        showSizeChanger: true,
        showQuickJumper: true,
        size: 'small',
        showTotal: (total) => `总共 ${total}条`
      }">
      <template #bodyCell="{ record, column }">
        <div v-if="column.key === 'avatar'">
          <a-image :src="record.avatar" class="avatar-image" alt=""></a-image>
        </div>
        <div v-if="column.key === 'name'">
          <InfoPopover v-bind="record">
            <span>{{ record.name }}</span>
          </InfoPopover>
        </div>
      </template>
    </Table>
  </div>
</template>

<style scoped>
.history-table-wrapper {
  width: 100%;
  overflow-x: auto;
  max-width: 100%;
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

:deep(.ant-table-container) {
  overflow-x: auto;
  width: 100%;
}

:deep(.ant-table) {
  table-layout: auto !important;
  width: 100% !important;
  max-width: 100% !important;
}

:deep(.ant-table-content) {
  width: 100%;
  overflow-x: auto;
}

:deep(.ant-table-body) {
  overflow-x: auto !important;
}

:deep(table) {
  width: 100% !important;
  table-layout: fixed !important;
}
</style>
