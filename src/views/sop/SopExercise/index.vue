<script setup lang="ts">
import { ref, computed } from 'vue'
import SopExercise from './SopExercise.vue';
import SopExam from './SopExam.vue';
import SopCheck from './SopCheck/SopCheck.vue'
import MyExam from './MyExam.vue';
import SopTrust from './SopTrust/SopTrust.vue';
import SopTrustAI from './SopTrust/SopTrustAI.vue'
import SopTrustAITest from './SopTrust/SopTrustAITest.vue'
import { adminRights, hasServePool } from '@qianshou/service';
import SopSpeechPractice from './SopSpeechPractice/SopSpeechPractice.vue';
import SopSpeechPracticeManage from './SpeechPracticeManage/SpeechPracticeManage.vue';
import SopSpeechDemo from '../SopRecords/SaleRecords.vue'
import SopVad from './SopVad/SopVad.vue'

const hasSalesandServeManagerRight = adminRights?.allSale || adminRights?.isDeveloperOrAdmin || adminRights?.isServeManager
const hasManagerRight = adminRights?.isServeManager || adminRights?.isSalesManager || adminRights?.isDeveloperOrAdmin

const isProd = import.meta.env.VITE_APP_ENV === "production"
const devopTest = [
  { label: 'AI红娘建信训练', value: 'aiTest', background: 'linear-gradient(145deg, #e7a012 0%, #c6b798 100%)' },
  { label: '智能红娘', value: 'aiTest2', background: 'linear-gradient(145deg, rgb(251 214 248)  0%, #b565e8 100%)' }
]
const wecomSaleModes = [
  { label: '练习模式', value: 'test', background: 'linear-gradient(145deg, #FF58E4 0%, #69BAF9 100%)' },
  {
    label: '考试模式', value: 'exam', background: 'linear-gradient(145deg, #FE5C5C 0%, #9769F9 100%)'
  },
  { label: '聊天建信测试', value: 'trust', background: 'linear-gradient(145deg, #91D3A0 0%, #65C7DD 100%)' },

  {
    label: '预测检查', value: 'check', background: 'linear-gradient(112deg, #FF00C3 3.89%, #FE5C5C 100%)'
  }
]

const saleModes = [

  hasSalesandServeManagerRight ? { label: '问答练习', value: 'SopSpeechPractice', background: 'linear-gradient(145deg, #ADCEFF 0%, #78D3D3 100%)' } : {},
  hasManagerRight ? { label: '语音练习管理', value: 'SpeechPracticeManage', background: 'linear-gradient(145deg, #53B605 0%, #FFCA10 100%)' } : {},
  hasSalesandServeManagerRight ? { label: '录音库', value: 'SpeechPracticeDemo', background: 'linear-gradient(145deg, #058BB6 0%, #10F1FF 100%)' } : {},
  adminRights?.isDeveloper ? { label: '电话对练(本地vad)', value: 'phone', background: 'linear-gradient(145deg, #FF6464 0%, #FFA800 100%)' } : {},
]
const showSaleModes = computed(() => saleModes.filter(item => item.value))
const businessType = ref('sale')
const type = ref('test') // 选模式 练习模式/考试模式

const isStart = ref(false)

const choose = (val) => {
  type.value = val;
  isStart.value = true
}
</script>

<template>
  <div class="app-container">
    <template v-if="!isStart">

      <template v-if="adminRights?.isDeveloper && !isProd">
        <h3>研发专用实验</h3>
        <div class="opts type-opt">
          <template v-for="item in devopTest" :key="item.value">
            <div class="card" :style="{ background: item.background }" v-if="item?.value" @click="choose(item.value)">{{
              item.label }}</div>
          </template>
        </div>
      </template>
      <template v-if="hasServePool('weChatSopTrainingManual') || adminRights?.allWeCom">
        <h3>用户运营培训
          <MyExam />
        </h3>

        <div class="opts type-opt">
          <template v-for="item in wecomSaleModes" :key="item.value">
            <div class="card" :style="{ background: item.background }" v-if="item?.value" @click="choose(item.value)">{{
              item.label }}</div>
          </template>
        </div>
      </template>

      <template v-if="showSaleModes?.length && !adminRights?.allWeCom">
        <h3>电话培训</h3>
        <div class="opts type-opt">
          <template v-for="item in saleModes" :key="item.value">
            <div class="card second" :style="{ background: item.background }" v-if="item?.value"
              @click="choose(item.value)">{{ item.label }}</div>
          </template>
        </div>
      </template>


    </template>
    <template v-else>
      <SopExercise @close="isStart = false" v-if="isStart && type === wecomSaleModes[0].value"
        :businessType="businessType" />
      <SopExam @close="isStart = false" v-if="isStart && type === wecomSaleModes[1].value"
        :businessType="businessType" />
      <SopTrust @close="isStart = false" v-if="isStart && type === wecomSaleModes[2].value" />

      <SopCheck @close="isStart = false" v-if="isStart && type === wecomSaleModes[3].value" />

      <SopTrustAI @close="isStart = false" v-if="!isProd && isStart && type === devopTest[0].value" />
      <SopTrustAITest @close="isStart = false" v-if="!isProd && isStart && type === devopTest[1].value" />

      <SopSpeechPractice @close="isStart = false" v-if="isStart && type === saleModes[0].value" />
      <SopSpeechPracticeManage @close="isStart = false" v-if="isStart && type === saleModes[1].value" />
      <SopSpeechDemo @close="isStart = false" v-if="isStart && type === saleModes[2].value" />
      <SopVad @close="isStart = false" v-if="isStart && type === saleModes[3].value" />

    </template>
  </div>
</template>

<style scoped>
.app-container {
  position: relative;
}

.opts {
  width: 100%;
  display: flex;
  gap: 16px;
  flex: 1;
}

.type-opt {
  margin-bottom: 50px;
}

.card {
  width: 260px;
  height: 150px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.card:hover {
  opacity: 0.7;
}
</style>