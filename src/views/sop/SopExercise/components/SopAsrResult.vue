<script setup lang="ts">
import { CheckCircleFilled, CloseCircleFilled, LoadingOutlined } from '@ant-design/icons-vue'
import type { InspectionResult } from '../composables/useBaseQuestion'

defineProps<{
  count?: number
  loading: boolean
  result?: InspectionResult
  resultText: string
}>()

</script>
<template>

  <a-card bordered :headStyle="{ background: '#f0f0f0' }" size="small" title="解答">
    <div class="res-card">
      <p v-if="loading" class="loading" style="color:#6ecadf;font-size: 14px;">
        <LoadingOutlined /> 判断中，请稍后...
      </p>
      <template v-else>
        <p style="color:#ccc" v-if="!result?.content">(完成答题后，在此查看解答)</p>

        <template v-if="result?.inspectionResult === true">
          <p style="color:#31a331;">
            <CheckCircleFilled style="margin-right: 10px;" />
            {{ result?.totalScore ?? 0 }}分
          </p>
          <p class="reason">{{ result?.reason }}</p>
        </template>

        <template v-if="result?.inspectionResult === false">
          <p style="color:red;">
            <CloseCircleFilled style="margin-right: 10px;" />
            {{ result?.totalScore ?? 0 }}分
          </p>
          <p class="reason">{{ result?.reason }}</p>
        </template>

      </template>

    </div>
  </a-card>
  <a-card class="record" bordered :headStyle="{ background: '#f0f0f0' }" size="small" title="提交记录">
    <div class="record-card">
      <p v-if="loading" class="loading" style="color:#6ecadf;font-size: 14px;">
        <LoadingOutlined /> 识别中，请稍后...
      </p>
      <template v-else>
        <p v-if="count === undefined && result?.content"> {{ result?.content }}</p>
        <p v-else style="color:#ccc">(完成答题后，在此查看提交的答案)</p>
      </template>
    </div>
  </a-card>

</template>




<style lang="css" scoped>
.res-card {
  height: 15vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  overflow-y: scroll;
}

.record {
  flex: 1;
  overflow-y: scroll;
}

.record-card {
  min-height: 25vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  overflow-y: scroll;
}

.reason {
  font-size: 14px;
  color: #333;
}
</style>