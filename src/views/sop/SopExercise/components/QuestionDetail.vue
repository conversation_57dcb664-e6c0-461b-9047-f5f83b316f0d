<script setup lang="ts">
import { computed } from 'vue';
import { useStorage, getPrivateFileUrl, config } from '@qianshou/service';
import type { QuestionCon } from '../composables/useBaseQuestion'
import { Tag, Space } from 'ant-design-vue'
import { useAudioDomInject } from '../SopSpeechPractice/useAudio'

const difficultyMap = {
  'easy': '简单',
  'medium': '中等',
  'hard': '困难'
}


const props = defineProps<{
  type?: 'sale' | 'serve'
  question?: QuestionCon
  showStat?: boolean
}>()


const categoryEnums = computed(() => {
  if (props.type === 'sale') {
    return config?.value?.selectionEnum?.telemarketingQuestionCategory
  } else {
    return config?.value?.selectionEnum?.serveTelemarketingQuestionCategory
  }
})
const audioDom = useAudioDomInject()

const isFemale = useStorage<boolean>('培训声音播放男声女声', false)

</script>
<template>
  <h2>{{ `${question?.id}. ${question?.title}` }}</h2>
  <p>
    <Tag v-if="question?.category">{{categoryEnums?.find(x => x.value ===
      question?.category)?.label ?? ''}}</Tag>
    <Tag v-if="question?.difficulty"
      :color="question?.difficulty === 'easy' ? 'cyan' : question?.difficulty === 'medium' ? 'orange' : '#f50'">
      {{ difficultyMap[question?.difficulty] }}</Tag>
  </p>
  <h4 :style="{ margin: '16px 0' }"> 关键词提示:</h4>
  <p v-for="(item, ind) in question?.keywords" :key="ind" style="margin: 0">
    {{ item }}
  </p>
  <div style="flex: 1"></div>
  <Space style="margin-bottom: 10px;">
    <h4 style="margin: 0;">参考解答</h4>
    <a-switch v-model:checked="isFemale" checked-children="女声" un-checked-children="男声" />
  </Space>
  <audio ref="audioDom" controls class="audio" v-if="question?.answerRecord"
    :src="getPrivateFileUrl(isFemale ? question?.answerRecord?.female : question.answerRecord?.male)"
    controlslist="nodownload">
  </audio>
  <p v-if="showStat" class="sumary">
    <span>通过次数{{ question?.passedSubmissions ?? 0 }}</span>
    <span>提交次数{{ question?.totalSubmissions ?? 0 }}</span>
    <span>通过率{{ `${((question?.passRate ?? 0) * 100).toFixed(2)}%` }}</span>
  </p>
</template>
<style scoped>
:deep(.ant-card-body) {
  display: flex;
  flex-direction: column;
  height: calc(100% - 55px);
}

.sumary {
  display: flex;
  gap: 8px;
  margin-bottom: 0;
}

.audio {
  margin-bottom: 20px;
  width: calc(100% - 60px);
  height: 40px;
}
</style>