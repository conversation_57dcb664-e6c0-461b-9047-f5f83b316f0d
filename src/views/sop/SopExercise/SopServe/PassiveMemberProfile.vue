<script setup lang="ts">
/**
 * 被动方资料展示组件
 * 用于在type === 'passivetoMember'时展示被动方资料
 */
import { ref, computed, watch } from 'vue';
import { Descriptions, DescriptionsItem, Tag, Image } from 'ant-design-vue';
import { getV3SopTrainingServeMmMateStandardUserProfile, type UserInfoInfo, type UserInfoDetail } from '@qianshou/service';
import { joinDot } from '@qianshou/common';
import { basic } from '@/views/customer/MembershipInfo/InformationSummary/basic';

const props = defineProps<{
  userId?: number; // 被动方用户ID
}>();

// 用户资料数据
const userData = ref<{
  featureTag?: string[];
  interestTag?: string[];
  info?: UserInfoInfo;
  sideInfo?: UserInfoDetail;
  lifePhoto?: string[];
}>();

// 加载状态
const loading = ref(false);

// 获取被动方用户资料
const fetchUserProfile = async (userId: number) => {
  if (!userId) return;

  loading.value = true;
  try {
    const res = await getV3SopTrainingServeMmMateStandardUserProfile({
      userId // 这里的userId是被动方的ID
    });

    if (res?.data) {
      userData.value = {
        featureTag: res.data.featureTag,
        interestTag: res.data.interestTag,
        info: res.data.info,
        sideInfo: res.data.sideInfo,
        lifePhoto: res.data.lifePhoto,
      };
    }
  } catch (error) {
    console.error('获取被动方资料失败:', error);
  } finally {
    loading.value = false;
  }
};

// 监听userId变化
watch(() => props.userId, (newUserId) => {
  if (newUserId) {
    fetchUserProfile(newUserId);
  }
}, { immediate: true });

// 格式化更多资料
function displayDetail(detail?: UserInfoDetail) {
  return {
    "民族/宗教": joinDot(detail?.nation, detail?.religious),
    "情感经历描述": detail?.emotionDescription,
    "性格": detail?.character,
    "家庭情况": joinDot(
      detail?.familyType,
      detail?.fatherJob ? `父亲${detail?.fatherJob}` : "",
      detail?.motherJob ? `母亲${detail?.motherJob}` : ""
    ),
    "是否独生子女": joinDot(detail?.onlyChild, detail?.familyRank),
    "家庭情况详细": detail?.familyDetails,
    "是否吸烟": detail?.smokingStatus,
    "其它重要信息": detail?.notes,
  };
}

// 计算属性：更多资料数组
const detailArr = computed(() => Object.entries(displayDetail(userData.value?.sideInfo)));

// 计算属性：过滤后的更多资料数组（去除空值）
const filteredDetailArr = computed(() => detailArr.value.filter(item => !!item[1]));

// 计算属性：基本资料数组
const basicArray = computed(() => basic(userData.value?.info));
</script>

<template>
  <!-- 被动方资料展示组件 -->
  <div :class="{ 'loading-state': loading }">
    <!-- 特点标签 -->
    <template v-if="userData?.featureTag?.length">
      <h3 class="title">特点标签</h3>
      <div class="tags-container">
        <Tag class="tag" v-for="item in userData.featureTag" :key="item">{{ item }}</Tag>
      </div>
    </template>

    <!-- 基本资料 -->
    <Descriptions :column="2" :colon="false" title="基本资料">
      <DescriptionsItem v-for="item in basicArray" :key="item.label" :label="item.label + ':'">
        {{ item.value }}
        <el-tooltip placement="right-start" v-if="item.tooltip" :content="item.tooltip">
          <el-link type="primary" :underline="false">详情</el-link>
        </el-tooltip>
      </DescriptionsItem>
      <DescriptionsItem label="自我介绍:" :span="2" v-if="userData?.info?.description">
        <div>
          <div v-for="(item, index) in userData?.info?.description?.split(/\n+/)" :key="index">
            {{ item }}
          </div>
        </div>
      </DescriptionsItem>
    </Descriptions>

    <!-- 更多资料 -->
    <Descriptions v-if="filteredDetailArr.length > 0" :column="1" :colon="false" title="更多资料" size="small">
      <DescriptionsItem v-for="item in filteredDetailArr" :key="item[0]" :label="item[0] + ':'">
        {{ item[1] }}
      </DescriptionsItem>
    </Descriptions>

    <!-- 兴趣标签 -->
    <template v-if="userData?.interestTag?.length">
      <h3 class="title">兴趣标签</h3>
      <div class="tags-container">
        <Tag v-for="item in userData.interestTag" :key="item">{{ item }}</Tag>
      </div>
    </template>

    <!-- 生活照 -->
    <template v-if="userData?.lifePhoto?.length">
      <h3 class="title">生活照</h3>
      <div class="img-w">
        <Image class="img" :preview="false" v-for="item in userData.lifePhoto" :key="item" :src="item" alt="" />
      </div>
    </template>
  </div>
</template>

<style scoped>
.loading-state {
  opacity: 0.6;
  pointer-events: none;
}

.title {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.tag {
  margin-right: 6px;
  margin-bottom: 6px;
}

.img-w {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

:deep(.img) {
  display: block;
  width: 120px;
  height: 120px;
  object-fit: cover;
}
</style>
