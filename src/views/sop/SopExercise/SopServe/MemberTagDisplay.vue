<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { getV3UserTags } from "@qianshou/service";

// 是否为开发环境
const isDev = process.env.NODE_ENV === 'development';

const props = defineProps<{
  userId?: number;
}>();

// 特点标签数据
const featureTags = ref<string[]>([]);
// 兴趣标签数据
const interestTagData = ref<string[]>([]);
// 加载状态
const loading = ref(false);


// 获取标签数据
const fetchTags = async (userId: number) => {
  if (!userId) return;

  loading.value = true;
  try {
    // 直接调用API获取数据
    const res = await getV3UserTags({ user_id: userId });

    console.log('API 返回数据:', res);

    // 处理特点标签
    if (res?.feature_tag && res.feature_tag.length > 0) {
      featureTags.value = res.feature_tag;
    } else {
      featureTags.value = [];
    }


    if (res?.interest_tag && res.interest_tag.length > 0) {
      interestTagData.value = res.interest_tag;
    } else {
      interestTagData.value = [];
      console.log('没有兴趣标签数据');
    }
  } catch (e) {
    console.error('获取会员标签失败', e);
    featureTags.value = [];
    interestTagData.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听userId变化
watch(() => props.userId, (newUserId) => {
  if (newUserId) {
    fetchTags(newUserId);
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.userId) {
    fetchTags(props.userId);
  }
});
</script>

<template>
  <div class="member-tags-container">
    <!-- 加载中 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载会员标签...</div>
    </div>

    <template v-else>
      <!-- 兴趣标签 -->

      <div v-if="interestTagData.length > 0" class="feature-tags-section">
        <h3 class="section-title">兴趣标签</h3>
        <div class="container">
          <div class="tag" v-for="tag in interestTagData" :key="`interest-${tag}`">
            {{ tag }}
          </div>
        </div>
      </div>

      <!-- 特点标签 -->
      <div v-if="featureTags.length > 0" class="feature-tags-section">
        <h3 class="section-title">特点标签</h3>
        <div class="container">
          <div class="tag" v-for="tag in featureTags" :key="`feature-${tag}`">
            {{ tag }}
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <div v-if="featureTags.length === 0 && interestTagData.length === 0" class="no-tags">
        暂无会员标签数据
        <button v-if="isDev" @click="fetchTags(props.userId || 0)" class="debug-button">
          重新获取标签数据
        </button>
      </div>
    </template>
  </div>
</template>

<style scoped>
.member-tags-container {
  width: 100%;
}

.interest-tags-section {
  margin-bottom: 24px;
}

.feature-tags-section {
  margin-top: 24px;
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
  border-left: 3px solid #1890ff;
  padding-left: 10px;
}

/* 兴趣标签和特点标签共用样式 */
.container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
}

.tag {
  display: flex;
  border-radius: 16px;
  padding: 6px 12px;
  background-color: #fff;
  color: #000;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  font-size: 14px;
  line-height: 20px;
  margin: 5px;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
}

.no-tags,
.no-interest-tags {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
}

.debug-button {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-info {
  margin: 10px 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  overflow: auto;
}

.debug-info pre {
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
