<script setup lang="ts">
import { Tag, Space } from 'ant-design-vue';
import { GetV3SopTrainingTaskListResponse } from '@qianshou/service';

type statusStatType = Required<GetV3SopTrainingTaskListResponse>['data']['statusStat'];

defineProps<{
  statusStat?: statusStatType;
}>();
</script>
<template>
  <div>
    <Space>
      <Tag v-if="statusStat?.today" color="blue">
        今日完成率： {{ statusStat.today.completedRate ? `${(statusStat.today.completedRate * 100).toFixed(2)}%` : '0%' }}
      </Tag>
      <Tag v-if="statusStat?.today" color="green">
        今日成功率：{{ statusStat.today.succeedRate ? `${(statusStat.today.succeedRate * 100).toFixed(2)}%` : '0%' }}
      </Tag>
      <Tag v-if="statusStat?.all" color="blue">
        完成率： {{ statusStat.all.completedRate ? `${(statusStat.all.completedRate * 100).toFixed(2)}%` : '0%' }}
      </Tag>
      <Tag v-if="statusStat?.all" color="green">
        成功率： {{ statusStat.all.succeedRate ? `${(statusStat.all.succeedRate * 100).toFixed(2)}%` : '0%' }}
      </Tag>

    </Space>
  </div>
</template>



<style lang="scss" scoped></style>