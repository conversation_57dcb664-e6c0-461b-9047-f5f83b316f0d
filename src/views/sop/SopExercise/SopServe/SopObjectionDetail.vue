<script setup lang="ts">
import { onUnmounted, onMounted } from 'vue'
import { Button } from '@qianshou/ui'
import { useAudioDom } from '../SopSpeechPractice/useAudio'
import QuestionDetail from '../components/QuestionDetail.vue'
import SopAsrResult from '../components/SopAsrResult.vue'
import { useTimer } from '../composables/useTimer'
import { postSopStatus } from '../composables/util'
import { useBaseQuestion, getQuestion } from '../composables/useBaseQuestion'
const emits = defineEmits(['close'])
const total = 180

const { count, startTimer, stopTimer } = useTimer(total)
const props = defineProps<{
  taskId: number
  taskStatus: number
  questionId: number
  isFromManage?: boolean // 是否从管理页面进入
}>()

const {
  currentQuestion,
  result,
  loading,
  sendMessage,
  getTaskResult,
  resultText,
  startASR,
  stopASR,
} = useBaseQuestion()

const initQuestion = async () => {
  const question = await getQuestion({ questionID: props.questionId })
  currentQuestion.value = question
}

onMounted(() => {
  initQuestion()
  if (props.taskStatus === 1 || props.taskStatus === 2) {
    getTaskResult({ id: props.taskId })
  }
})

const { pause, toggle } = useAudioDom()

function stop() {
  stopASR(async (blob) => {
    await sendMessage(blob)
    stopTimer?.()

    if (!result.value) return
    await postSopStatus({
      id: props?.taskId,
      status: result.value?.inspectionResult ? 1 : 2,
      reason: result.value?.reason,
      answerRecordID: result.value?.answerRecordID
    })
  })

  toggle(true)
}

async function start() {
  pause()
  toggle(false)

  resultText.value = ''
  result.value = undefined

  startASR()
  startTimer(stop)
}

function destroy() {
  pause()
  stopASR() // stopASR现在会释放所有资源
  stopTimer?.()
}

const exit = () => {
  destroy()
  emits('close')
}

onUnmounted(() => {
  destroy()
})

</script>
<template>
  <div class="app-container">
    <div class="info">
      <a-alert style="flex:1" message="培训内容为内部资料, 严禁泄露" banner closable />
      <Button type="link" @click="exit">返回培训系统</Button>
    </div>

    <div class="chat">
      <a-card bordered :headStyle="{ background: '#f0f0f0' }" size="small" style="width: 60%"
        :title="`ID(${taskId}) 题目描述`">
        <QuestionDetail :question="currentQuestion" :showStat="false" type="serve" />
      </a-card>

      <div class=" rightSec" style="width: 30%">
        <SopAsrResult :loading="loading" :result="result" :resultText="resultText" :count="count" />

        <Button v-if="loading" class="submitBtn" disabled>正在提交...</Button>
        <Button v-else-if="typeof count === 'number'" class="submitBtn submitIng" @click="stop()">
          {{ `剩余${count}秒 点击停止` }}
        </Button>
        <Button v-else class="submitBtn" @click="start"
          :disabled="props.isFromManage || taskStatus === 1 || taskStatus === 2 || !!result">
          {{ ` 开始答题 限时${total}秒` }}
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat :global(.ant-tabs-tab) {
  font-size: 14px !important;
  height: 38px;
  font-weight: 500;
}

.chat {
  height: 75vh;
  margin-bottom: 40px;
  gap: 20px;
  display: flex;
}

.rightSec {
  height: 75vh;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info {
  width: calc(90% + 20px);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.submitBtn {
  width: 100%;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6ecadf;
  color: #fff;
  border: none;
}

.submitIng {
  background: #ff5500;
}
</style>