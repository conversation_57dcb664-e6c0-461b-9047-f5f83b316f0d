<script setup lang="ts">
import { ref } from 'vue';
import { ZoomDialog } from '@qianshou/ui';
import { Button } from 'ant-design-vue';

// Control the visibility of the drawer
const visible = ref(false);

// Toggle the drawer visibility
const toggleDrawer = () => {
  visible.value = !visible.value;
};
</script>

<template>
  <Button type="primary" size="small" @click="toggleDrawer" style="margin-left: 10px">说明</Button>

  <ZoomDialog v-if="visible" v-model="visible" title="今日待办说明" modal="rtl" style="background-color: #fcfaf7">
    <div class="sop-serve-intro">
      <div class="section">
        <h3>启动</h3>
        <p>会员刚刚买单，销售拉了微信服务群。你需要遵照标准的启动SOP，与客户对话，进行服务，并完成邀约首通电话。不按照SOP工作、遗漏关键点等，有可能会导致启动待办失败。</p>
      </div>

      <div class="section">
        <h3>首通</h3>
        <p>会员接受了首通电话，你需要参考首通SOP，和客户进行电话沟通。</p>
        <p>你需要在电话中，做到：</p>
        <ol>
          <li>让会员理解服务的价值；</li>
          <li>语气亲切，体现红娘的尽心尽力；</li>
          <li>让会员认同尽快见面很重要，并提供了见面时间；</li>
          <li>让会员知道接下来的安排，感觉进度挺快的；</li>
          <li>确认用户的要求和情况；</li>
          <li>为用户提供照片和资料方面优化的建议；</li>
          <li>向会员说明工作时间。</li>
        </ol>
        <p>请注意，细节丰富程度和生动程度也会进行考察。不按照SOP进行沟通、遗漏关键点等，可能导致首通待办失败。</p>
      </div>

      <div class="section">
        <h3>把会员介绍给被动方</h3>
        <p>你在为会员准备合适的推荐，第一步是把会员介绍给被动方。你需要根据会员的择偶标准，挑选符合会员要求的候选人，选择需要额外验证的的资料项（牵线红娘会帮忙完成），并写好具有吸引力的推荐语。</p>
        <p>请注意，你需要做到：</p>
        <ol>
          <li>被动方的条件要满足会员的核心要求</li>
          <li>选取活跃的被动方，否则可能无法收到回复</li>
          <li>避免踩雷，撞上会员的忌讳点</li>
        </ol>
        <p>不按照标准选取被动方，不仅可能导致待办失败，也有可能影响后续把被动方介绍给会员时的结果。</p>
      </div>

      <div class="section">
        <h3>把被动方推荐给会员</h3>
        <p>你在之前联系的被动方回复了，有一些表示愿意认识会员并见面。你需要好好包装被动方的情况，说明推荐的理由和他们适合的地方，让会员同意和被动方见面。</p>
      </div>

      <div class="section">
        <h3>异议处理</h3>
        <p>服务过程中，会员提出了一些尖锐的问题，你需要对问题进行处理。请确保你的回答覆盖了关键点和细节，并注意对话的生动程度。</p>
      </div>
    </div>
  </ZoomDialog>
</template>

<style scoped>
.sop-serve-intro {
  padding: 20px;
  max-width: 600px;
}

.section {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.section:last-child {
  border-bottom: none;
}

.section h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.section p {
  margin-bottom: 10px;
  line-height: 1.6;
}

.section ol {
  padding-left: 20px;
  margin-bottom: 10px;
}

.section li {
  margin-bottom: 5px;
  line-height: 1.6;
}
</style>
