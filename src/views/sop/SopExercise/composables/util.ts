import { message } from 'ant-design-vue';
import { postV3SopTrainingAiChatComplete, postV3SopTrainingTaskStatus } from '@qianshou/service';

export const handleSubmit = async (conversationId?: number, onSuccess?: () => void, onError?: (errMsg?: string) => void) => {
  if (!conversationId) {
    message.error('请选择聊天对象');
    return;
  }

  try {
    const { data } = await postV3SopTrainingAiChatComplete({
      conversationID: conversationId
    }, {
      timeout: 60 * 1000
    });

    if (data?.completed) {
      onSuccess?.();
    } else {
      onError?.(data?.reason);
    }
  } catch (error) {
    message.error('提交失败');
  }
};

export const postSopStatus = async (params: { id: number; status: number; reason?: string; answerRecordID?: number }) => {
  try {
    await postV3SopTrainingTaskStatus({
      id: params.id,
      status: params.status,
      reason: params.reason,
      answerRecordID: params.answerRecordID
    });
    console.log('postSopStatus提交成功')
  } catch (error) {
    console.log('postSopStatus提交失败', error)
  }
}

/**
 * 将Blob对象转换为Base64字符串
 * @param {Blob} blob - 要转换的Blob对象
 * @returns {Promise<string>} 返回一个Promise，解析为base64字符串
 */
export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = reader.result as string;
      resolve(base64String);
    };
    reader.onerror = () => {
      reject(new Error('Blob转Base64失败'));
    };
    reader.readAsDataURL(blob);
  });
}