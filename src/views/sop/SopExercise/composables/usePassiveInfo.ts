import { ref, computed } from 'vue';
import type { Item } from "@/views/customer/components/EditDetail/config";
import type { ConversationData } from './useConversation';

export type BaseItem = Item & { value: any };

type HideDepends = { key: string; value: string[] };

const needCheck = (x: BaseItem, infos: BaseItem[]) => {
  if (!x?.hideDepends?.length) return true;
  return !x.hideDepends.some((y: HideDepends) =>
    y.value?.includes(infos?.find(z => z.name === y.key)?.value)
  );
};

const checkNoValArrLength = (arr: BaseItem[]) => {
  if (!arr?.length) return 0;
  return arr.filter(x =>
    needCheck(x, arr) && (x?.value === '' || x?.value === undefined || x?.value === null || x?.value?.length === 0)
  ).length;
};


export const transformBaseInfoToApiFormat = (baseInfo: BaseItem[]): Record<string, any> => {
  return baseInfo.reduce((acc, item) => {
    if (item.name) {
      acc[item.name] = item.value;
    }
    return acc;
  }, {} as Record<string, any>);
};


export function usePassiveInfo() {
  const baseInfo = ref<BaseItem[]>([]);
  const passiveInfo = ref<string>('');
  const passiveInfoList = ref<Record<string, any>>({});

  const noValueCount = computed(() => {
    return checkNoValArrLength(baseInfo.value);
  });

  const updatePassiveInfo = (conversationInfo?: ConversationData) => {
    if (!conversationInfo) return;

    passiveInfo.value = conversationInfo?.extra?.mockPassiveUserInfo?.baseInfo ?? '';
    passiveInfoList.value = conversationInfo?.extra?.mockPassiveUserInfo ?? {};
  };

  return {
    baseInfo,
    passiveInfo,
    passiveInfoList,
    noValueCount,
    updatePassiveInfo
  };
} 