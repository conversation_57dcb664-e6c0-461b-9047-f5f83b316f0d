import { ref } from 'vue'
import { postV3SopTrainingTelemarketingInspection, PostV3SopTrainingTelemarketingInspectionResponse, GetV3SopTrainingTelemarketingQuestionResponse, getV3SopTrainingTask } from '@qianshou/service'
import { message } from 'ant-design-vue'
import { uploadTelemarketingAnswer, getV3SopTrainingTelemarketingQuestion, postV3SopTrainingTelemarketingRecognize } from '@qianshou/service'
import RecordRTC, { StereoAudioRecorder } from "recordrtc";
import { blobToBase64 } from './util';

export interface BaseQuestionOptions {
  onQuestionUpdate?: () => Promise<void>
}

export type InspectionResult = PostV3SopTrainingTelemarketingInspectionResponse['data']
export type QuestionCon = Required<Required<GetV3SopTrainingTelemarketingQuestionResponse>['data']>['question']

export const uploadFile = async (file: Blob, questionID: number): Promise<string> => {
  try {
    const uploadedUrl = await uploadTelemarketingAnswer(file, questionID)
    return uploadedUrl ?? ''
  } catch (error) {
    console.error('上传文件失败:', error)
    return ''
  }
}

export const getQuestion = async ({ questionID }: { questionID: number }) => {
  try {
    const { data } = await getV3SopTrainingTelemarketingQuestion({ questionID })
    if (!data) {
      throw new Error('获取题目详情失败：返回数据为空')
    }
    return data.question
  } catch (error) {
    console.error('获取题目详情失败:', error)
    throw error
  }
}

export const useBaseQuestion = (options?: BaseQuestionOptions) => {
  const currentQuestion = ref<QuestionCon>()
  const result = ref<InspectionResult>()
  const loading = ref(false)
  const resultText = ref('')

  const getTaskResult = async ({ id }: { id?: number }) => {
    if (!id) return
    const res = await getV3SopTrainingTask({
      id,
    })
    result.value = res?.data?.teleMarketingAnswerRecord
  }

  const sendMessage = async (file?: Blob) => {
    try {
      const questionID = currentQuestion.value?.id
      if (typeof questionID !== 'number') {
        message.error('题目不存在')
        loading.value = false
        return
      }
      loading.value = true

      if (!file) {
        message.error('录音为空')
        loading.value = false
        return
      }

      // 将两个任务并行执行
      const [res1, recordUrl] = await Promise.all([
        // 任务1: 将blob转换为base64并调用语音识别API
        (async () => {
          const base64Data = await blobToBase64(file)
          return postV3SopTrainingTelemarketingRecognize({
            message: base64Data
          })
        })(),
        // 任务2: 上传文件获取recordUrl
        uploadFile(file, questionID)
      ])
      resultText.value = res1?.data?.message ?? ''

      if (!resultText.value) {
        message.error('录音文本为空')
        loading.value = false
        return
      }
      const res = await postV3SopTrainingTelemarketingInspection({
        content: resultText.value,
        questionID,
        recordUrl,
      }, {
        timeout: 60 * 1000,
      })

      result.value = res?.data

      // 调用更新回调
      if (options?.onQuestionUpdate) {
        await options.onQuestionUpdate()
      }
    } catch (error) {
      console.error('提交答案失败:', error)
    } finally {
      loading.value = false
    }
  }


  let mediaStream: MediaStream | undefined
  let recorder: RecordRTC | undefined

  /** 释放所有录音资源 */
  const releaseResources = () => {
    // 释放录音机资源
    if (recorder) {
      try {
        recorder.reset()
        recorder.destroy()
        recorder = undefined
      } catch (error) {
        console.error('释放录音资源失败:', error)
      }
    }

    // 释放媒体流资源
    if (mediaStream) {
      mediaStream.getAudioTracks().forEach(track => {
        track.stop()
      })
      mediaStream = undefined
    }
  }

  /** 开始录音 */
  const startASR = async () => {
    resultText.value = ''

    // 确保先释放之前的资源
    releaseResources()

    try {
      mediaStream = await window.navigator.mediaDevices.getUserMedia({
        audio: true
      });

      recorder = new RecordRTC(mediaStream, {
        // 后端要求单通道，16K采样率，PCM
        type: "audio",
        recorderType: StereoAudioRecorder,
        mimeType: "audio/wav",
        numberOfAudioChannels: 1,
        desiredSampRate: 16000,
        disableLogs: true,
        timeSlice: 200,
      });

      recorder.startRecording()
    } catch (error) {
      console.error('启动录音失败:', error)
      releaseResources() // 出错时释放资源
    }
  }

  /**
   * 停止录音并释放所有资源
   * 如果提供了callback，会在获取到blob后调用callback
   */
  const stopASR = (callback?: (blob: Blob) => void) => {
    if (!recorder) return

    try {
      recorder.stopRecording(async () => {
        try {
          // 获取录音数据
          const blob: Blob = recorder.getBlob()
          callback?.(blob)
        } finally {
          // 无论如何都要释放所有资源
          releaseResources()
        }
      })
    } catch (error) {
      console.error('停止录音失败:', error)
      releaseResources() // 出错时也要释放资源
    }
  }

  return {
    currentQuestion,
    getTaskResult,
    result,
    loading,
    sendMessage,
    resultText,
    startASR,
    stopASR,
  }
}