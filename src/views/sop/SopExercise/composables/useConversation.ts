import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  getV3SopTrainingAiChatConversation,
  type GetV3SopTrainingAiChatConversationResponse
} from '@qianshou/service';

export type ConversationData = Required<GetV3SopTrainingAiChatConversationResponse>['data'];

export function useConversation({ conversationId, taskId }: { conversationId?: number, taskId?: number }) {
  const conversationInfo = ref<ConversationData>();

  const userInfo = computed(() => {
    return conversationInfo.value?.mockUserInfo;
  });
  const userID = computed(() => {
    return conversationInfo.value?.mockUserID;
  });
  const userMateDemand = computed(() => {
    return conversationInfo.value?.extra?.userMateDemand;
  });

  const passiveAvatar = computed(() => {
    return conversationInfo.value?.extra?.mockPassiveUserInfo?.avatar;
  });

  const passiveInfo = computed(() => {
    return conversationInfo.value?.extra?.mockPassiveUserInfo?.baseInfo;
  });

  const passiveUserId = computed(() => {
    return conversationInfo.value?.extra?.mockPassiveUserInfo?.userID;
  });

  const loading = ref(false);

  const getInfo = async () => {

    try {
      loading.value = true;
      const { data } = await getV3SopTrainingAiChatConversation({
        conversationID: conversationId,
        taskID: taskId
      });

      conversationInfo.value = data;
    } catch (error) {
      message.error('获取会话信息失败');
    } finally {
      loading.value = false;
    }
  };


  return {
    conversationInfo,
    loading,
    getInfo,
    userID,
    userInfo,
    passiveInfo,
    passiveUserId,
    userMateDemand,
    passiveAvatar
  };
}
