import { ref, onMounted, onUnmounted, onActivated, onDeactivated } from 'vue';
import { message } from 'ant-design-vue';
import {
  getV3SopTrainingAiChatMessages,
  postV3SopTrainingAiChatMessage,
} from '@qianshou/service';
import type { MessageItem } from '@/views/sop/SopExercise/SopTrust/index';

export function useConversationMsgs(conversationId?: number) {
  const conversationMsgs = ref<MessageItem[]>([]);
  const editMsg = ref('');
  const loading = ref(false);


  const timer = ref()
  const resetTimer = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  }
  const initTimer = () => {
    resetTimer()
    timer.value = setInterval(() => {
      refreshMessages()
    }, 1000)
  }

  onMounted(() => {
    initTimer()
  })

  onUnmounted(() => {
    resetTimer()
  })

  onActivated(() => {
    initTimer()
  })

  onDeactivated(() => {
    resetTimer()
  })

  const refreshMessages = async () => {
    if (!conversationId) return;

    try {
      loading.value = true;
      const res = await getV3SopTrainingAiChatMessages({
        conversationID: conversationId
      });
      conversationMsgs.value = res?.data?.list ?? [];
    } catch (error) {
      message.error('获取消息列表失败');
    } finally {
      loading.value = false;
    }
  };

  const sendMessage = async () => {
    if (!conversationId) {
      message.error("请选择聊天对象");
      return;
    }

    const content = editMsg.value.trim();
    if (!content) {
      message.error("不能发送空内容");
      return;
    }

    try {
      loading.value = true;
      const res = await postV3SopTrainingAiChatMessage({
        conversationID: conversationId,
        content
      });

      if (res?.code === 0) {
        editMsg.value = '';
        await refreshMessages();
      }
    } catch (error: any) {
      const code = error?.response?.data?.code;
      if (code === -80001 || code === -80002) {
        editMsg.value = '';
        await refreshMessages();
      } else {
        message.error('发送消息失败');
      }
    } finally {
      loading.value = false;
    }
  };


  return {
    conversationMsgs,
    editMsg,
    loading,
    refreshMessages,
    sendMessage
  }
}

