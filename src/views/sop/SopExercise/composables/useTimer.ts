import { ref } from 'vue'
// 计时器composable
export const useTimer = (totalSeconds: number) => {
  const count = ref<number>()
  let timer: number = 0

  const startTimer = (onComplete: () => void) => {
    const initial = Date.now()
    count.value = totalSeconds

    timer = window.setInterval(() => {
      const currentCount = Math.round(totalSeconds - (Date.now() - initial) / 1000)
      if (currentCount >= 0) {
        count.value = currentCount
      } else {
        stopTimer()
        onComplete()
      }
    }, 1000)
  }

  const stopTimer = () => {
    window.clearInterval(timer)
    count.value = undefined
  }

  return {
    count,
    startTimer,
    stopTimer
  }
}

