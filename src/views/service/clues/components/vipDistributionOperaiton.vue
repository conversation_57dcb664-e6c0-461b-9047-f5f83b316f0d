<script>
import { request } from "@qianshou/service";

export default {
  props: {
    props: {
      type: Object,
      default: undefined,
    },
    operation: {
      type: String,
      default: "add",
    },
    options: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      form: {
        types: "vip", // 分配方式
        name: "", // 策略名称
        summary: "", // 策略简介
        detail: [
          {
            leads: undefined,
            choose: [
              {
                saleList: undefined,
                matchmakerSortBy: undefined,
                serveMatchmakerSortBy:
                  this.options?.serveMatchmakerSortBy[0].value,
                newServingDays: undefined,
                leadsLimit: {
                  gender: {
                    1: undefined,
                    2: undefined,
                  },
                  total: undefined,
                },
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    disabled() {
      return this.operation === "view";
    },
  },
  methods: {
    async viewOrEdit() {
      if (this.props.id) {
        const res = await request({
          method: "get",
          url: "/v2/leads-config/allocate",
          params: {
            id: this.props.id,
          },
        });
        this.form = {
          ...this.form,
          ...res,
          detail: res?.detail,
        };
      }
      this.dialogFormVisible = true;
    },
    addStrategy() {
      this.form.offline.push({
        saleList: undefined,
        matchmakerSortBy: undefined,
        serveMatchmakerSortBy: this.options?.serveMatchmakerSortBy[0].value,
        newServingDays: undefined,
        leadsLimit: {
          gender: {
            1: undefined,
            2: undefined,
          },
          total: undefined,
        },
        leads: [
          {
            value: undefined,
          },
        ],
      });
    },
    addChannel() {
      this.form.detail.push({
        leads: undefined,
        choose: [
          {
            saleList: undefined,
            matchmakerSortBy: undefined,
            serveMatchmakerSortBy: this.options?.serveMatchmakerSortBy[0].value,
            newServingDays: undefined,
            leadsLimit: {
              gender: {
                1: undefined,
                2: undefined,
              },
              total: undefined,
            },
          },
        ],
      });
    },
    removeChannel(index) {
      this.form.detail.splice(index, 1);
    },
    addChannelMarket(index) {
      this.form.detail?.[index]?.choose?.push({
        saleList: undefined,
        matchmakerSortBy: undefined,
        serveMatchmakerSortBy: this.options?.serveMatchmakerSortBy[0].value,
        newServingDays: undefined,
        leadsLimit: {
          gender: {
            1: undefined,
            2: undefined,
          },
          total: undefined,
        },
      });
    },
    removeChannelMarket(index, ind) {
      this.form.detail?.[index]?.choose?.splice(ind, 1);
    },
    handleSubmit() {
      this.$refs.form.validate().then(async (valid) => {
        if (valid) {
          await request({
            method: "POST",
            url: "/v2/leads-config/allocate",
            data: {
              ...this.form,
              detail: this.form.detail,
              status: undefined,
            },
          });
        } else {
          return false;
        }
        this.$emit("change");
        this.dialogFormVisible = false;
        this.$refs.form.resetFields();
      });
    },

    updateChannelOrder(prev, next) {
      // vue2中对监听数组的splice方法进行了重载
      const arr = this.form.detail.splice(prev, 1, this.form.detail[next]);
      this.form.detail.splice(next, 1, ...arr);
    },

    updateChannelMarketOrder(index, prev, next) {
      // vue2中对监听数组的splice方法进行了重载
      const arr = this.form.detail?.[index]?.choose?.splice(
        prev,
        1,
        this.form.detail?.[index]?.choose?.[next]
      );
      this.form.detail?.[index]?.choose?.splice(next, 1, ...arr);
    },
  },
  emits: ["change"],
};
</script>

<template>
  <span>
    <a-button v-if="operation === 'add'" type="primary" plain @click="dialogFormVisible = true">新增分配策略</a-button>
    <a-button v-else-if="operation === 'view'" type="link" @click="viewOrEdit()">查看</a-button>
    <a-button v-else type="link" @click="viewOrEdit()">编辑</a-button>
    <a-modal v-model:visible="dialogFormVisible" title="分配策略" :maskClosable="false"
      :footer="operation === 'view' ? null : undefined" width="60%" @ok="handleSubmit">
      <a-form ref="form" :model="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
        <a-form-item label="策略名称" name="name" :rules="[
          { required: true, message: '请输入策略名称', trigger: 'blur' },
        ]">
          <a-input v-model:value="form.name" autocomplete="off" maxlength="50" :disabled="disabled" />
        </a-form-item>
        <a-form-item label="策略简介" name="summary">
          <a-input v-model:value="form.summary" autocomplete="off" maxlength="50" :disabled="disabled" />
        </a-form-item>
        <a-button type="primary" plain :disabled="disabled" @click="addChannel()">新增</a-button>
        <section v-for="(strategy, index) in form.detail" :key="index">
          <a-form-item :label="'会员类型' + (index + 1)" :name="['detail', index, 'leads']" required>
            <a-input-group compact>
              <a-select v-model:value="strategy.leads" :options="options.vip" :disabled="disabled"
                style="min-width: 200px" />
              <a-button v-if="index !== 0 && operation !== 'view'" type="primary"
                @click="updateChannelOrder(index - 1, index)">上移</a-button>
              <a-button v-if="index !== form.detail.length - 1 && operation !== 'view'" type="primary"
                @click="updateChannelOrder(index, index + 1)">下移</a-button>
            </a-input-group>
          </a-form-item>

          <div v-for="(market, ind) in strategy.choose" :key="ind">
            <a-form-item :label="'红娘' + (ind + 1)" :name="['detail', index, 'choose', ind, 'saleList']" required>
              <a-input-group compact>
                <a-select v-model:value="market.saleList" :options="options.saleList" :disabled="disabled"
                  style="min-width: 200px" />
                <a-button v-if="ind === strategy.choose.length - 1" type="primary" :disabled="disabled"
                  @click="addChannelMarket(index)">增加</a-button>
                <a-button v-if="strategy.choose.length !== 1" danger :disabled="disabled"
                  @click="removeChannelMarket(index, ind)">删除</a-button>

                <a-button v-if="ind !== 0 && operation !== 'view'" danger
                  @click="updateChannelMarketOrder(index, ind - 1, ind)">上移</a-button>
                <a-button v-if="ind !== strategy.choose.length - 1 && operation !== 'view'
                  " danger @click="updateChannelMarketOrder(index, ind, ind + 1)">下移</a-button>
              </a-input-group>
            </a-form-item>

            <a-form-item label="分配方式" :name="['detail', index, 'choose', ind, 'serveMatchmakerSortBy']" :rules="[
              {
                required: true,
                message: '请选择分配方式',
                trigger: 'blur',
              },
            ]">
              <a-radio-group v-model:value="market.serveMatchmakerSortBy" :disabled="disabled">
                <template v-for="x in options.serveMatchmakerSortBy" :key="x.value">
                  <a-radio v-if="x.label === '日新分' || x.value === 'NewServingInDays$'
                    " :value="x.value">
                    <a-input-number v-model:value="market.newServingDays" autocomplete="off" class="minWidth" :min="0"
                      :disabled="disabled" />
                    {{ x.label }}
                  </a-radio>
                  <a-radio v-else :value="x.value">{{ x.label }}</a-radio>
                </template>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="分配限制">
              <a-input-group compact>
                <a-form-item-rest>
                  <a-input style="width: 38px" placeholder="男" disabled />
                </a-form-item-rest>
                <a-form-item :name="[
                  'detail',
                  index,
                  'choose',
                  ind,
                  'leadsLimit',
                  'gender',
                  1,
                ]">
                  <a-input-number v-model:value="market.leadsLimit.gender['1']" class="minWidth" :disabled="disabled"
                    :min="0" />
                </a-form-item>
                <a-form-item-rest>
                  <a-input style="width: 38px" placeholder="条" disabled />
                </a-form-item-rest>

                <a-form-item-rest>
                  <a-input style="width: 38px" placeholder="女" disabled />
                </a-form-item-rest>
                <a-form-item :name="[
                  'detail',
                  index,
                  'choose',
                  ind,
                  'leadsLimit',
                  'gender',
                  2,
                ]">
                  <a-input-number v-model:value="market.leadsLimit.gender['2']" class="minWidth" :disabled="disabled"
                    :min="0" />
                </a-form-item>
                <a-form-item-rest>
                  <a-input style="width: 38px" placeholder="条" disabled />
                </a-form-item-rest>

                <a-form-item-rest>
                  <a-input style="width: 58px" placeholder="此类" disabled />
                </a-form-item-rest>
                <a-form-item :name="[
                  'detail',
                  index,
                  'choose',
                  ind,
                  'leadsLimit',
                  'kindCap',
                ]">
                  <a-input-number v-model:value="market.leadsLimit.kindCap" class="minWidth" :disabled="disabled" />
                </a-form-item>
                <a-form-item-rest>
                  <a-input style="width: 38px" placeholder="条" disabled />
                </a-form-item-rest>
              </a-input-group>
            </a-form-item>
          </div>
          <div v-if="form.detail.length !== 1" class="delete">
            <a-button danger plain :disabled="disabled" @click="removeChannel(index)">删除</a-button>
          </div>
          <a-divider v-if="index !== form.detail.length - 1" />
        </section>
      </a-form>
    </a-modal>
  </span>
</template>

<style scoped>
.delete {
  display: flex;
  justify-content: flex-end;
}

.minWidth {
  width: 80px;
}
</style>
