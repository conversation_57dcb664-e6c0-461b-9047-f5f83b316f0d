<script lang="ts">
export default {
  name: "OrderConfirm",
};
</script>

<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { cityFilter } from "@/utils/customer";
import type { Dayjs } from "dayjs";
import { objToString, omit } from '@qianshou/common';
import {
  getV3PerformanceOrderConfirms,
  GetV3PerformanceOrderConfirmsRequest,
  GetV3PerformanceOrderConfirmsResponse,
  postV3PerformanceOrderConfirms,
} from "@qianshou/service"
import { OrganizationCascader } from "@qianshou/component";
import { StaffSelect } from "@qianshou/component";
import { dayjsMonth, dayjsMinutes } from "@qianshou/common";
import { openDetailTab } from "@/utils/open";
import { hasServePool, adminRights, getServeStageLabel } from "@qianshou/service";
import { DateRangePicker } from "@qianshou/ui";
import dayjs from "dayjs";

type TableRecord = Required<Required<GetV3PerformanceOrderConfirmsResponse>["data"]>["list"][number];

const ActiveKeys = {
  closed: "未开启订单",
  opened: "已开启订单",
} as const;

const activeKey = ref<typeof ActiveKeys.closed | typeof ActiveKeys.opened>(
  ActiveKeys.closed
);

const isOpened = computed(() => activeKey.value === ActiveKeys.opened);

/** 确认/取消发薪按钮权限控制 */
const salaryPer = hasServePool("HRsalary");

const defaultForm = {
  page: 1,
  size: 100,
  uid: undefined,
  org_ids: [],
  status: computed(() =>
    isOpened.value ? ["started", "payoff"] : ["notStarted"]
  ),
  start_service_time: undefined,
  month: undefined,
  matchmaker_id: undefined,
  range: (isOpened.value ? undefined : [dayjs().subtract(1, 'month'), dayjs()]) as [Dayjs, Dayjs],
};
const form: GetV3PerformanceOrderConfirmsRequest & { month?: Dayjs; range: [Dayjs, Dayjs] } = reactive({
  ...defaultForm,
});

const update = () => {
  selectedRowKeys.value = undefined;
  setPage(1);
  getList();
};

const changeTabs = () => {
  Object.assign(form, defaultForm);
  update();
};

const selectedRowKeys = ref<number[]>();
const diaVisible = ref(false);
const month = ref<Dayjs>();
const currentPage = ref(1);
const total = ref(0);
const data = ref<Required<GetV3PerformanceOrderConfirmsResponse>["data"]["list"]>([]);

const onSelectChange = (v: number[]) => {
  selectedRowKeys.value = v;
};

function getQueryString() {
  return isOpened.value ? {
    ...omit(form, 'page', 'size', 'month', 'range'),
    uid: typeof form.uid === "string" ? Number(form.uid) : form.uid,
    start_service_time: form.month
      ? form.month.valueOf() * 1000000
      : undefined,
  } : {
    ...omit(form, 'page', 'size', 'month', 'range'),
    uid: typeof form.uid === "string" ? Number(form.uid) : form.uid,
    pay_start_time: form.range?.[0] ? form.range?.[0]?.valueOf() * 1000000 : undefined,
    pay_end_time: form.range?.[1] ? form.range?.[1]?.valueOf() * 1000000 : undefined,
  }
}

const getList = async () => {
  try {
    const res = await getV3PerformanceOrderConfirms({
       ...getQueryString(),
          page: form.page,
      size: form.size,
    })

    data.value =  res?.data?.list?.map((item) => ({ ...item, key: item.uid }));
    total.value = res?.data?.total ?? 0;
  } catch (e) {
    data.value = [];
    total.value = 0;
  }
};
getList();

const setPage = (x: number) => {
  currentPage.value = x;
  form.page = x;
};

const handleTableChange = (pagination: { current: number }) => {
  selectedRowKeys.value = undefined;
  setPage(pagination?.current ?? 1);
  getList();
};

const search = () => {
  setPage(1);
  getList();
};

const columns = computed(() =>
  [
    {
      title: "用户ID",
      dataIndex: "uid",
      key: "uid",
    },
    {
      title: "姓名",
      key: "userName",
    },
    {
      title: "性别",
      key: "gender",
      width: "50px",
    },
    {
      title: "年龄",
      dataIndex: "age",
      width: "50px",
    },
    {
      title: "现居地",
      key: "residence",
    },
    {
      title: "会员阶段",
      key: "serve_stage",
    },
    {
      title: "订单支付时间",
      key: "pay_time",
      width: "120px",
    },
    {
      title: "服务",
      dataIndex: "serve_matchmaker",
    },
    {
      title: "主订单ID",
      dataIndex: "order_id",
    },
    {
      title: "订单金额",
      dataIndex: "order_price",
      width: "100px",
    },
    isOpened.value && {
      title: "订单开启月份",
      dataIndex: "start_service_time",
    },
    {
      title: "销售",
      dataIndex: "sale_matchmaker",
    },
    {
      title: "归属业绩",
      dataIndex: "performance",
    },
    isOpened.value && {
      title: "操作",
      key: "actions",
    },
  ]
    .filter((item) => !!item)
    .map((item) => ({ ...item, align: "center" }))
);
const edit = async (action: "openOrder" | "pay") => {
  if (!selectedRowKeys.value) {
    return ElMessage.warning({
      showClose: true,
      message: "请选择订单",
    });
  }
  if (action === "openOrder") {
    diaVisible.value = true;
  } else {
    try {
      await ElMessageBox.confirm("确认该订单为发薪状态吗?", "提示", {
        type: "warning",
      });
      editOrder("pay");
    } catch { }
  }
};

const submit = async () => {
  if (!month.value) {
    return ElMessage.warning({
      showClose: true,
      message: "请选择订单开启时间",
    });
  }
  editOrder("openOrder");
};

const editOrder = async (action: "openOrder" | "pay") => {
  try {
    await postV3PerformanceOrderConfirms({
      ids: selectedRowKeys.value,
      status: action === "openOrder" ? "started" : "payoff",
      operation: "confirm",
      start_service_time: month.value
        ? month.value.valueOf() * 1000000
        : undefined,
    });
    diaVisible.value = false;
    ElNotification.success("成功");
    update();
  } catch {
    ElNotification.error("失败");
  }
};

const cancel = async (i: TableRecord, action: "openOrder" | "pay") => {
  if (!i.id) return;
  try {
    await ElMessageBox.confirm(
      action === "openOrder"
        ? "取消开启服务后，此单将不计入销售业绩"
        : "取消【已发薪】状态后，该订单会出现在后续薪酬统计中。",
      action === "openOrder"
        ? "确认取消开启服务吗?"
        : "确认取消【已发薪】状态吗?",
      {
        type: "warning",
      }
    );
    await postV3PerformanceOrderConfirms({
      ids: [i.id],
      operation: "cancel",
      status: action === "openOrder" ? "started" : "payoff",
    });
    update();
  } catch { }
};

const handleCustomRow = (record: TableRecord) => {
  return {
    onDblclick: () => {
      openDetailTab(String(record?.uid));
    },
  };
};
</script>

<template>
  <div class="app-container">
    <a-tabs type="card" v-model:activeKey="activeKey" @change="changeTabs">
      <a-tab-pane :tab="ActiveKeys.closed" :key="ActiveKeys.closed"></a-tab-pane>
      <a-tab-pane :tab="ActiveKeys.opened" :key="ActiveKeys.opened"></a-tab-pane>
    </a-tabs>
    <div>
      <a-form layout="inline">
        <a-form-item>
          <a-input size="small" v-model:value="form.uid" placeholder="按客户ID搜索" style="width: 120px" />
        </a-form-item>
        <a-form-item>
          <StaffSelect size="small" v-model="form.matchmaker_id" filt-sub="1" placeholder="按红娘姓名/ID搜索"
            style="width: 160px" />
        </a-form-item>
        <a-form-item>
          <OrganizationCascader size="small" v-model="form.org_ids" placeholder="按组织架构选择" />
        </a-form-item>
        <a-form-item v-if="isOpened">
          <a-date-picker size="small" placeholder="按订单开启月份搜索" v-model:value="form.month" picker="month"
            style="width: 160px" />
        </a-form-item>
        <a-form-item v-else>
          <DateRangePicker v-model="form.range" :placeholder="['起始', '结束']" style="width: 240px" />
        </a-form-item>
        <a-form-item class="btns">
          <a-button type="primary" size="small" @click="search">搜索</a-button>

          <a-button v-if="isOpened && salaryPer" type="danger" size="small" @click="edit('pay')">确认发薪</a-button>
          <a-button v-if="!adminRights.allSale && !isOpened" type="danger" size="small"
            @click="edit('openOrder')">开启服务</a-button>
          <a-button type="primary" style="margin-left: 20px" size="small"
            :href="`/v3/performance/order-confirms/export?${objToString(getQueryString())}`" download
            @click="search">导出为Excel</a-button>
        </a-form-item>
      </a-form>
      <a-table bordered size="small" :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
      }" :custom-row="handleCustomRow" rowKey="id" :columns="columns" :pagination="{
        current: currentPage,
        pageSize: 100,
        total: total,
        showSizeChanger: false,
        showTotal: (total: number) => `共${total}条`,
      }" :data-source="data" @change="handleTableChange">
        <template #bodyCell="{ record, column }">
          <span v-if="column.key === 'gender'">
            {{
              `${record.gender === "female"
                ? "女"
                : record.gender === "male"
                  ? "男"
                  : ""
              } `
            }}
          </span>
          <span v-if="column.dataIndex === 'start_service_time'">
            {{ dayjsMonth(record.start_service_time * 1000) }}
          </span>
          <span v-if="column.key === 'userName'">
            {{ `${record.nick_name} (${record.real_name})` }}</span>
          <span v-if="column.key === 'residence'">
            {{ cityFilter(record.residence) }}</span>
          <span v-if="column.key === 'pay_time'">
            {{ dayjsMinutes(record.pay_time) }}</span>

          <span v-if="column.key === 'serve_stage'">
            {{ getServeStageLabel(record?.serve_stage) }}
          </span>
          <div v-if="column.key === 'actions'">
            <el-link v-if="record.status === 'started' && !adminRights.allSale" type="primary"
              @click="cancel(record, 'openOrder')">取消开启</el-link>
            <template v-if="record.status === 'payoff'">
              <span>已发薪</span>
              <el-link v-if="salaryPer" type="primary" @click="cancel(record, 'pay')">取消</el-link>
            </template>
          </div>
        </template>
      </a-table>
      <a-modal v-model:visible="diaVisible" title="选择开启服务时间" width="50%" @ok="submit()">
        <a-date-picker v-model:value="month" picker="month" />
      </a-modal>
    </div>
  </div>
</template>
<style scoped lang="scss">
:deep(.ant-form) {
  margin: 10px 0;
}

:deep(.ant-form-item-control-input) {
  min-height: 24px;
  height: 24px;
}

.ant-form-inline .el-form-item {
  margin-right: 16px;
}

.ant-select,
.ant-input {
  font-size: 12px;
  line-height: 22px;
}

:deep(.ant-form-item-label > label) {
  height: 26px;
}

.btns {
  .ant-btn {
    margin: 6px;
  }
}
</style>
