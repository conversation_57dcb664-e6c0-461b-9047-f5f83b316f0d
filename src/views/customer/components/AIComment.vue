<template>
  <div>
    <!-- 空状态 -->
    <el-card v-if="!commentData">
      <h4>{{ title }}</h4>
      <div v-if="status === 'in-progress'" class="empty-body">
        <span>生成中...</span>
        <el-button link type="primary" @click="fetchComment" class="refresh-btn">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      <div v-else class="empty-body">
        <el-button link type="primary" @click="generateComment" class="generate-btn">立即分析</el-button>
      </div>
    </el-card>

    <!-- 生成状态 -->
    <el-card v-else>
      <h4>{{ title }}</h4>
      <div class="content-preview">{{ commentData }}</div>
      <el-link type="primary" :underline="false" @click="showFullComment">查看全部</el-link>
    </el-card>

    <!-- 侧边栏 -->
    <Drawer v-model:visible="drawerVisible" width="450" :zIndex="2000" placement="right">
      <Tabs v-model:activeKey="activeTabKey">
        <TabPane key="user-portrait" :tab="title">
          <div v-html="parsedFullContent"></div>
          <div v-if="status === 'in-progress'" class="empty-body">
            <span>生成中...</span>
            <el-button link type="primary" @click="fetchComment" class="refresh-btn">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <div v-else class="empty-body">
            <el-button @click="generateComment" link type="primary">用户已更新? 重新生成</el-button>
          </div>
        </TabPane>
        <TabPane v-if="isServe" key="first-call" tab="首通SOP">
          <div v-html="parsedFirstCallContent"></div>
          <div v-if="firstCallStatus === 'in-progress'" class="empty-body">
            <span>生成中...</span>
            <el-button link type="primary" @click="fetchFirstCall" class="refresh-btn">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <div v-else class="empty-body">
            <el-button @click="generateFirstCall" link type="primary">用户已更新? 重新生成</el-button>
          </div>
        </TabPane>
      </Tabs>
    </Drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { marked } from 'marked';
import { Drawer, Tabs, TabPane } from 'ant-design-vue';
import { ElButton, ElMessage } from 'element-plus';
import { getIsServe, useUser } from '@qianshou/service';
import { postV3AiColdReading, getV3AiColdReading } from '@qianshou/service';

// 定义状态类型
type CommentStatus = 'in-progress' | 'completed';

// 获取用户信息
const userInfo = useUser();

const isServe = getIsServe();

const title = computed(() => {
  return isServe?.value ? '会员深度分析' : '评价信息'
});

// 评价信息数据
const commentData = ref<string | null>(null);
const status = ref<CommentStatus>();
const drawerVisible = ref(false);
const loading = ref(false);
const activeTabKey = ref('user-portrait');

// 首通SOP数据
const firstCallData = ref<string | null>(null);
const firstCallStatus = ref<CommentStatus>();
const firstCallLoading = ref(false);

// 解析Markdown内容
const parsedFullContent = computed(() => {
  if (!commentData.value) return '';
  return marked.parse(commentData.value);
});

// 解析首通SOP内容
const parsedFirstCallContent = computed(() => {
  if (!firstCallData.value) return '';
  return marked.parse(firstCallData.value);
});

// 定义内容类型
type ContentType = 'user-portrait' | 'first-call';

// 通用获取信息函数
const fetchContent = async (type: ContentType) => {
  if (!userInfo?.value?.id) {
    ElMessage.warning('用户ID不存在');
    return;
  }

  // 根据类型设置对应的状态和数据
  const isUserPortrait = type === 'user-portrait';
  const loadingRef = isUserPortrait ? loading : firstCallLoading;

  try {
    loadingRef.value = true;
    const res = await getV3AiColdReading({
      userID: userInfo.value.id,
      type,
      bizType: isServe?.value ? 'serve' : 'sale',
    });

    // 更新对应的状态
    if (isUserPortrait) {
      status.value = res.data?.status as CommentStatus;
      if (res?.data?.userPortrait) {
        commentData.value = res.data.userPortrait;
      }
    } else {
      firstCallStatus.value = res.data?.status as CommentStatus;
      if (res?.data?.userPortrait) {
        firstCallData.value = res.data.userPortrait;
      }
    }
  } catch (error) {
    console.error(`获取${isUserPortrait ? '评价信息' : '首通SOP'}失败:`, error);
  } finally {
    loadingRef.value = false;
  }
};

// 通用生成信息函数
const generateContent = async (type: ContentType) => {
  if (!userInfo?.value?.id) {
    ElMessage.warning('用户ID不存在');
    return;
  }

  // 根据类型设置对应的状态和数据
  const isUserPortrait = type === 'user-portrait';
  const loadingRef = isUserPortrait ? loading : firstCallLoading;
  const successMessage = isUserPortrait ? '评价信息生成已提交' : '首通SOP生成已提交';

  try {
    loadingRef.value = true;
    // 调用生成接口
    await postV3AiColdReading({
      userID: userInfo.value.id,
      type,
      bizType: isServe?.value ? 'serve' : 'sale',
    });

    // 更新对应的状态
    if (isUserPortrait) {
      status.value = 'in-progress';
    } else {
      firstCallStatus.value = 'in-progress';
    }

    ElMessage.success(successMessage);
  } catch (error) {
    console.error(`生成${isUserPortrait ? '评价信息' : '首通SOP'}失败:`, error);
  } finally {
    loadingRef.value = false;
  }
};

// 便捷函数 - 获取评价信息
const fetchComment = () => fetchContent('user-portrait');

// 便捷函数 - 生成评价信息
const generateComment = () => generateContent('user-portrait');

// 便捷函数 - 获取首通SOP信息
const fetchFirstCall = () => fetchContent('first-call');

// 便捷函数 - 生成首通SOP信息
const generateFirstCall = () => generateContent('first-call');

// 显示完整评价信息
const showFullComment = () => {
  drawerVisible.value = true;
};

// 组件挂载时获取评价信息
onMounted(() => {
  fetchComment();
  fetchFirstCall();
});
</script>

<style scoped>
.empty-body {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  gap: 10px;
}

.generate-btn {
  width: 120px;
}

.refresh-btn {
  margin-left: 10px;
}

.comment-footer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

h4 {
  margin-bottom: 14px;
}

.content-preview {
  word-break: break-word;
  max-height: 120px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
</style>