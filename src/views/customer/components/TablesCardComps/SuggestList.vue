<script setup lang="ts">
import { ref, computed } from 'vue'
import MakeDate from "../action/MakeDate.vue";
import RelationshipForm from "./RelationshipForm.vue";
import { openDetailTab, openPreSaleRecommend } from "@/utils/open";
import { Button, UserName } from "@qianshou/ui";
import { adminRights, config, type SelectionItem, useSelections, setAchievement, useUserInfo, contactFeedback, useUserEnoughInfo, getRefreshServiceLog } from "@qianshou/service";
import Pagination from "@/components/Pagination/index.vue";
import { Tag, Space, message, Modal } from 'ant-design-vue'
import { dayjsMinutes } from '@qianshou/common';
import { ReportMeeting, ReportMarried } from '@qianshou/component';
import RejectReasonModal from './FeedbackRejectModal.vue';
import SecondFeedbackModal from './SecondFeedbackModal.vue';
import MakeMeeting from "../action/MakeMeeting.vue";
import { AIMatchAnalysisButton } from "@qianshou/component"

const refreshServiceLog = getRefreshServiceLog();
const { isEnoughInfo, notFills } = useUserEnoughInfo()
const modalVisible = ref(false)
const feedbackColor = [
  "blue",
  "yellow",
  "cyan",
  "green",
  "volcano",
  "geekblue",
  "magenta",
  "purple",
]

function getMemershiStagColor(tag) {
  switch (tag) {
    case "员工":
    case '被动方':
      return "processing";
    case "会员":
      return "error";
    case "过期会员":
    default:
      return "default";
  }
}

function getCurrentProgressColor(row: SelectionItem) {
  const index = config.value?.selectionEnum?.achievement?.findIndex?.(x => x.value === row.currentProgress)
  return feedbackColor[(index ?? 0) % 8]
}

function getCurrentProgressLabel(row: SelectionItem) {
  return config.value?.selectionEnum?.achievement?.find?.(x => x.value === row.currentProgress)?.label
}
const {
  otherUserId,
  getList,
  pagination,
  list,
  loading,
  selectionFeedbackChange,
} = useSelections()
getList();

const { setUserInfo, userInfo } = useUserInfo()

defineExpose({ getList })

function jumpToDetail(row) {
  openDetailTab(String(row.otherUser?.id));
}

const current = ref<SelectionItem>()
const makeDate = ref()

function showCreateDate(row: SelectionItem, modalType: string) {
  current.value = row;
  makeDate.value?.handleDate(
    row.userId,
    row.otherUser?.id,
    "4",
    modalType,
    row.id
  );
}

const handleWechat = (row) => {
  current.value = row;
  if (!row?.otherUser?.canExchangeWechat || !isEnoughInfo.value) {
    modalVisible.value = true
    return
  }
  showCreateDate(row, 'wechat')
}

const reportMarried = ref<InstanceType<typeof ReportMarried>>()
function showCreateMarried(row: SelectionItem) {
  const { gender, name, realName, id } = row?.otherUser ?? {};
  current.value = row;
  id && reportMarried.value?.show({
    otherUserInfo: { gender, name, realName, id }, userInfo: {
      gender: userInfo?.value?.info?.gender === 'male' ? 1 : 2,
      realName: userInfo?.value?.info?.realName,
      name: userInfo?.value?.info?.name,
      id: userInfo?.value?.id
    }
  });
}

const reportMeeting = ref<InstanceType<typeof ReportMeeting>>()
function showCreateMeeting(row: SelectionItem) {
  const { gender, name, realName, id } = row?.otherUser ?? {};
  current.value = row;
  reportMeeting.value?.show({ gender, name, realName, id });
}

const makeMeeting = ref<InstanceType<typeof MakeMeeting>>()
function showMakeMeeting(row: SelectionItem) {
  current.value = row;
  makeMeeting.value?.show(row?.otherUser?.id);
}

const relationshipForm = ref<InstanceType<typeof RelationshipForm>>()
function handleShowRelationshipForm(row: SelectionItem, modalType: string) {
  current.value = row;
  // 确保用户ID存在
  if (!userInfo?.value?.id) {
    message.error('缺少用户ID');
    return;
  }

  relationshipForm.value?.handleShow({
    type: modalType,
    otherUser: row.otherUser,
    userId: userInfo.value.id
  });
}

/** 撤销（微信，结婚，见面, 预约见面）*/
const revoke = async (row: SelectionItem, modalType: 'wechat' | 'meet' | 'married' | 'setUpMeeting') => {
  if (!row?.userId || !row.otherUser?.id) return;
  try {
    await setAchievement({
      userId: row?.userId,
      otherUserId: row.otherUser?.id,
      serviceType: "revoke-" + modalType,
    });
    message.success('撤销成功')
    getList()
    setUserInfo?.();
  } catch { }
}

/** 成功更新 */
async function update() {
  if (current.value) {
    await selectionFeedbackChange(current.value, {
      feedback: 'contactAccepted',
      passiveFeedback: 'contactAccepted',
    })
  }
  setUserInfo?.()
  refreshServiceLog?.()
}


const visible = ref(false)
const secondVisible = ref(false)
const currentUserId = ref()
const currentRow = ref()
const currentOtherUserId = ref()
const rejectTitle = ref()
const secondFeedbackType = ref()

const options = computed(() => [{ label: '未反馈', value: 0 }, ...(config?.value?.selectionEnum?.meetingContactStatus ?? [])])


const handleFeedBackSelect = (val, row, userId, otherId) => {
  const { gender, realName, id } = row?.otherUser ?? {};
  currentRow.value = row
  if (val !== 'contactRejected') {
    //TODO: 区分会员被动方 selectionFeedbackChange(row, { passiveFeedback: val })
    selectionFeedbackChange(row, userId === userInfo?.value?.id ? { feedback: val } : { passiveFeedback: val })
  } else {
    rejectTitle.value = userId === userInfo?.value?.id ? `会员（${userInfo?.value?.info?.realName?.charAt(0)},${userInfo?.value?.info?.gender === "female" ? "女" : "男"},${userInfo?.value?.id}）拒绝接触被动方的原因是？` : `被动方（${realName?.charAt(0)},${gender === 2 ? '女' : '男'},${id}）拒绝接触会员的原因是？`
    currentUserId.value = userId
    currentOtherUserId.value = otherId
    visible.value = true
  }
}

const handleSecondFeedback = (val, row, userId, otherId) => {
  currentRow.value = row
  if (![contactFeedback.contactRejected, contactFeedback.contactAccepted].includes(val)) {
    selectionFeedbackChange(row, userId === userInfo?.value?.id ? { secondFeedback: val } : { passiveSecondFeedback: val })
  } else {
    const { gender, realName, id } = row?.otherUser ?? {};
    const label = val === contactFeedback.contactRejected ? '拒绝接触' : '愿意接触'
    rejectTitle.value = userId === userInfo?.value?.id ? `会员（${userInfo?.value?.info?.realName?.charAt(0)},${userInfo?.value?.info?.gender === "female" ? "女" : "男"},${userInfo?.value?.id}）深沟后${label}被动方` : `被动方（${realName?.charAt(0)},${gender === 2 ? '女' : '男'},${id}）深沟后${label}会员`
    currentUserId.value = userId
    secondVisible.value = true
    secondFeedbackType.value = val
  }
}

const handelMeetFeedback = (val, row, userId) => {
  const { gender, name, realName, id } = row?.otherUser ?? {};
  current.value = row;
  reportMeeting.value?.show({ gender, name, realName, id }, undefined, userId === userInfo?.value?.id ? 'member' : 'other', val);

}

const handleSubmit = (val) => {
  selectionFeedbackChange(currentRow.value, currentUserId.value === userInfo?.value?.id ? { secondFeedback: secondFeedbackType.value } : { passiveSecondFeedback: secondFeedbackType.value }, { ...val, key: undefined })
  secondVisible.value = false
}

const handleRejectSubmit = () => {
  selectionFeedbackChange(currentRow.value, currentUserId.value === userInfo?.value?.id ? { feedback: 'contactRejected' } : { passiveFeedback: 'contactRejected' })
  visible.value = false
}

const handleMarriedUpdate = () => {
  setUserInfo?.()
  getList?.()
}

const canEdit = computed(() => adminRights?.isQualityControl || adminRights.isDeveloper)

</script>

<template>
  <Space wrap style="margin-bottom: 8px;">
    搜索ID:
    <el-input v-model="otherUserId" placeholder="客户ID" clearable size="small" style="width: 180px" />
    <Button @click="getList()">搜索</Button>
  </Space>
  <el-table size="small" :data="list" row-key="id" border fit highlight-current-row style="width: 100%"
    @row-dblclick="jumpToDetail" v-loading="loading" element-loading-text="请给我点时间！">

    <el-table-column align="center" label="用户ID" width="100px">
      <template v-slot="{ row }">
        <span>{{ row.otherUser?.id }}</span>
        <div v-if="row.isSaleRecommended" class="pre-sale">售前推荐</div>
      </template>
    </el-table-column>

    <el-table-column width="120px" label="姓名" align="center">
      <template v-slot="{ row }">
        <UserName v-if="row?.otherUser?.avatar" v-bind="row?.otherUser">
          <Tag v-for="tag in row?.otherUser?.tag" :key="tag" :color="getMemershiStagColor(tag)">
            {{ tag }}
          </Tag>
        </UserName>
      </template>
    </el-table-column>

    <el-table-column width="115px" label="推荐时间" align="center">
      <template v-slot="{ row }">
        <span>{{ dayjsMinutes(row.createdTime) }}</span>
      </template>
    </el-table-column>

    <el-table-column min-width="200px" label="红娘推荐语" align="center">
      <template v-slot="{ row }">
        <span>{{ row.matchMakerEvaluation }}</span>
      </template>
    </el-table-column>


    <el-table-column min-width="60px" label="服务推荐序号" prop="serveRecommendNumber" align="center" />


    <template v-if="config?.selectionEnum?.feedback?.length">
      <el-table-column label="被动方推荐反馈" align="center" width="120px">
        <template v-slot="{ row }">
          <a-select style="width: 100%" :value="row.passiveFeedback" size="small" :bordered="false"
            @select="(val) => handleFeedBackSelect(val, row, row.otherUser.id, userInfo?.id)">
            <a-select-option v-for="(item, ind) in config?.selectionEnum?.feedback" :key="item.value"
              :label="item.label" :value="item.value">
              <a-badge :color="feedbackColor[ind % 8]" :text="item.label" />
            </a-select-option>
          </a-select>
        </template>
      </el-table-column>
      <el-table-column label="会员推荐反馈" align="center" width="120px">
        <template v-slot="{ row }">
          <a-select style="width: 100%" :value="row.feedback" size="small" :bordered="false"
            @select="(val) => handleFeedBackSelect(val, row, userInfo?.id, row.otherUser.id)">
            <a-select-option v-for="(item, ind) in config?.selectionEnum?.feedback" :key="item.value"
              :label="item.label" :value="item.value">
              <a-badge :color="feedbackColor[ind % 8]" :text="item.label" />
            </a-select-option>
          </a-select>
        </template>
      </el-table-column>


      <el-table-column label="会员见面初印象" align="center" width="180px">
        <template v-slot="{ row }">
          <a-select style="width: 100%" :value="row.meetingMemberContactStatus"
            :disabled="!canEdit || row.meetingStatus !== 'done'" size="small" :bordered="false"
            @select="(val) => handelMeetFeedback(val, row, userInfo?.id,)">
            <a-select-option v-for="(item, ind) in options" :key="item.value" :label="item.label" :value="item.value">
              <a-badge :color="feedbackColor[ind % 8]" :text="item.label" />
            </a-select-option>
          </a-select>
        </template>
      </el-table-column>

      <el-table-column label="被动方见面初印象" align="center" width="180px">
        <template v-slot="{ row }">
          <a-select style="width: 100%" :value="row.meetingPassiveContactStatus"
            :disabled="!canEdit || row.meetingStatus !== 'done'" size="small" :bordered="false"
            @select="(val) => handelMeetFeedback(val, row, row.otherUser.id)">
            <a-select-option v-for="(item, ind) in options" :key="item.value" :label="item.label" :value="item.value">
              <a-badge :color="feedbackColor[ind % 8]" :text="item.label" />
            </a-select-option>
          </a-select>
        </template>
      </el-table-column>



      <el-table-column label="会员深沟后反馈" align="center" width="120px">
        <template v-slot="{ row }">
          <a-select style="width: 100%" :value="row.secondFeedback" size="small" :bordered="false"
            @select="(val) => handleSecondFeedback(val, row, userInfo?.id, row.otherUser.id)">
            <a-select-option v-for="(item, ind) in config?.selectionEnum?.feedback" :key="item.value"
              :label="item.label" :value="item.value">
              <a-badge :color="feedbackColor[ind % 8]" :text="item.label" />
            </a-select-option>
          </a-select>
        </template>
      </el-table-column>
      <el-table-column label="被动方深沟后反馈" align="center" width="120px">
        <template v-slot="{ row }">
          <a-select style="width: 100%" :value="row.passiveSecondFeedback" size="small" :bordered="false"
            @select="(val) => handleSecondFeedback(val, row, row.otherUser.id, userInfo?.id)">
            <a-select-option v-for="(item, ind) in config?.selectionEnum?.feedback" :key="item.value"
              :label="item.label" :value="item.value">
              <a-badge :color="feedbackColor[ind % 8]" :text="item.label" />
            </a-select-option>
          </a-select>
        </template>
      </el-table-column>
    </template>

    <el-table-column width="80px" label="最新进展" align="center">
      <template v-slot="{ row }">
        <Tag :color="getCurrentProgressColor(row)" size="small">{{ getCurrentProgressLabel(row) }}</Tag>
      </template>
    </el-table-column>

    <el-table-column min-width="80px" label="操作" align="center" fixed="right">
      <template v-slot="{ row }">
        <Button v-if="row.preSaleURL" type="link" color="#409eff" @click="openPreSaleRecommend(row.preSaleURL)">查看卡片
        </Button>

        <Button color="#67c23a" type="link" @click="jumpToDetail(row)">查看</Button>

        <AIMatchAnalysisButton :otherUserId="row?.otherUser?.id" />

        <a-dropdown v-if="!adminRights?.allSale" placement="topLeft" :trigger="['click']">
          <Button type="link" @click.prevent>
            更多
          </Button>
          <template #overlay>
            <a-menu>
              <a-menu-item v-if="row.achievement?.includes?.('schedule-dating')">
                <Button type="link" color="#ff7875" disabled>已视频约会</Button>
              </a-menu-item>
              <a-menu-item v-else @click="showCreateDate(row, 'dating')">
                <Button type="link" color="#ff7875">视频约会</Button>
              </a-menu-item>
              <a-menu-item v-if="row.achievement?.includes?.('schedule-wechat')">
                <Button type="link" color="#faad14" @click="revoke(row, 'wechat')"> 撤销微信交换录入 </Button>
              </a-menu-item>
              <a-menu-item v-else @click="handleWechat(row)">
                <Button type="link" color="#faad14"> 微信 </Button>
              </a-menu-item>


              <a-menu-item v-if="row.meetingStatus === 'pending'">
                <Button type="link" color="#409eff" @click="revoke(row, 'setUpMeeting')"> 取消预约 </Button>
              </a-menu-item>
              <a-menu-item v-if="!row.meetingStatus || row.meetingStatus === 'cancel'" @click="showMakeMeeting(row)">
                <Button type="link" color="#1890ff">预约见面</Button>
              </a-menu-item>

              <template v-if="adminRights?.isQualityControl || adminRights.isDeveloper">
                <a-menu-item v-if="row.achievement?.includes?.('schedule-meet')">
                  <Button type="link" color="#409eff" @click="revoke(row, 'meet')"> 撤销见面录入 </Button>
                </a-menu-item>
                <a-menu-item v-else @click="showCreateMeeting(row)">
                  <Button type="link" color="#1890ff">见面</Button>
                </a-menu-item>
              </template>

              <a-menu-item v-if="row.achievement?.includes?.('schedule-intercourse')"
                @click="handleShowRelationshipForm(row, 'relieve-intercourse')">
                <Button type="link" color="#800080">解除交往</Button>
              </a-menu-item>
              <a-menu-item v-else @click="handleShowRelationshipForm(row, 'schedule-intercourse')">
                <Button type="link" color="#800080"> 交往 </Button>
              </a-menu-item>
              <a-menu-item v-if="row.achievement?.includes?.('schedule-courtship')"
                @click="handleShowRelationshipForm(row, 'relieve-courtship')">
                <Button type="link" color="#800080"> 解除恋爱 </Button>
              </a-menu-item>
              <a-menu-item v-else @click="handleShowRelationshipForm(row, 'schedule-courtship')">
                <Button type="link" color="#800080">恋爱</Button>
              </a-menu-item>
              <a-menu-item v-if="row.achievement?.includes?.('married')">
                <Button type="link" color="#ff7875" @click="revoke(row, 'married')">撤销结婚录入</Button>
              </a-menu-item>
              <a-menu-item v-else @click="showCreateMarried(row)">
                <Button type="link" color="#ff7875">结婚</Button>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <slot name="passive"
          v-bind="{ passiveContactPermission: row?.otherUser.passiveContactPermission, id: row.otherUser.id }"></slot>
      </template>
    </el-table-column>
  </el-table>

  <RejectReasonModal v-if="currentUserId && visible" v-model:visible="visible" @cancel="() => visible = false"
    @submit="handleRejectSubmit" :title="rejectTitle" :userId="currentUserId" :otherUserId="currentOtherUserId" />

  <SecondFeedbackModal v-if="currentUserId && secondVisible" v-model:visible="secondVisible"
    @cancel="() => secondVisible = false" @submit="handleSubmit" :type="secondFeedbackType" :title="rejectTitle"
    :userId="currentUserId" />


  <Pagination v-show="pagination.total > 0" :total="pagination.total" v-model:page="pagination.page"
    v-model:limit="pagination.limit" @pagination="() => getList()" />

  <MakeDate ref="makeDate" @update="update" />

  <RelationshipForm ref="relationshipForm" @update="update" />

  <ReportMeeting ref="reportMeeting" @update="update" />

  <ReportMarried ref="reportMarried" @update="handleMarriedUpdate" />

  <Modal title="提示" :zIndex="2000" width="500px" v-model:visible="modalVisible" :closable="false" :footer="null">
    <p>
      <template v-if="!isEnoughInfo">
        {{ userInfo?.info?.name }}因 <span style="color:red">{{ notFills?.join("、") }}</span>未填写，
      </template>
      <template v-if="!current?.otherUser?.canExchangeWechat">
        {{ current?.otherUser?.name }}因 <span style="color:red">{{ current?.otherUser?.notCompleteFields?.join("、")
        }}</span>未填写，
      </template>无法被标记为被动方。请补充后再标记。
    </p>
    <p class="modal-btn">
      <Button type="default" @click="() => modalVisible = false">关闭</Button>
      <Button type="primary" @click="() => { modalVisible = false; jumpToDetail(current) }">去编辑被动方资料</Button>
    </p>
  </Modal>

  <MakeMeeting ref="makeMeeting" @update="getList" />
</template>



<style lang="scss" scoped>
.modal-btn {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn-intercourse {
  color: #800080;
}

.btn-intercourse:hover {
  color: purple;
}

.btn-intercourse:hover::after {
  border-color: purple;
}

.btn-courtship {
  color: #ffc0cb;
}

.btn-courtship:hover {
  color: pink;
}

.btn-courtship:hover::after {
  border-color: pink;
}

.el-table :deep(td) {
  position: relative;
  overflow: hidden;
}

.pre-sale {
  background-color: #f56c6c;
  color: #fff;
  font-size: 12px;
  position: absolute;
  top: -3px;
  right: -32px;
  padding: 0px 17px;
  transform: rotate(45deg) scale(0.5);
}
</style>
