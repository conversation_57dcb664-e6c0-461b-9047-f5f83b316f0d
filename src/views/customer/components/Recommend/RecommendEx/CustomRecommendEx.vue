<script lang="ts" setup>
import { ref } from "vue";
import { ZoomDialog } from "@qianshou/ui";
import { Textarea, message, Checkbox } from "ant-design-vue";
import { ElMessageBox } from 'element-plus'
import { useRecommendInfo } from '@/hooks/recommend/useRecommendInfo'
import { useMarkingTag } from '../useMarkingTag'
import { usePhotos, useAvatarUrl, useShowAvatarDialog } from '../usePhotos'
import { useUserInfo, getRefreshServiceLog, getIsServe, getV3MmRecommendUserBase, postV3MmRecommend } from "@qianshou/service";
import Avatar from '../Avatar.vue'
import MyVoice from '../MyVoice.vue'
import FeatureTag from './FeatureTag.vue'
// import InterestTag from '../InterestTag.vue'
// import Photos from '../Photos.vue'
// import BaseInfo from '../BaseInfo.vue'


const isServe = getIsServe()

const { userInfo } = useUserInfo();

const refreshServiceLog = getRefreshServiceLog();

const { data, submitInfo, infoOrigin, initInfos } = useRecommendInfo();

const props = defineProps<{
  modelValue: boolean;
  userID?: number; // otherUserID
  reason?: string;
  uid?: number[]; // 择偶批量推荐
  recommendType?: 1 | 2 | 3 // 1：售前推荐 2：服务红娘单推 3：服务红娘批量推
}>();


const emits = defineEmits(["update:modelValue", "update"]);

const abouts = ref<{
  aboutFamily?: string;
  aboutHobbies?: string;
  aboutCharacter?: string;
  recommendReason?: string;
}>({
  recommendReason: props.reason,
});

const useDesc = ref(true);
const useFeatureTag = ref(true);
const useVoice = ref(true)
const useInterestTag = ref(true)

// 头像
useShowAvatarDialog()
const { avatarUrl, setAvatarUrl } = useAvatarUrl()

const { checkedPhotos } = usePhotos()

const { markingTag } = useMarkingTag()

const getInfo = async (userID) => {
  if (!userID) return;
  const { data: resData } = await getV3MmRecommendUserBase({ userID })
  data.value = resData;
  if (resData?.param) {
    initInfos(resData)
    useVoice.value = resData?.param?.useVoice ?? false
    useInterestTag.value = resData?.param?.useInterestTag ?? false
    useFeatureTag.value = resData?.param?.useMarkingTag ?? false
    abouts.value = {
      aboutFamily: resData?.param?.aboutFamily,
      aboutHobbies: resData?.param?.aboutHobbies,
      aboutCharacter: resData?.param?.aboutCharacter,
      recommendReason: props.reason
    }
    useDesc.value = resData?.param?.baseUsed?.includes('description') ?? false
    if (resData?.param?.base?.avatar && [...(resData?.photos ?? []), ...(resData?.lifePhotosByMatchmaker ?? []), ...(resData?.photosByAI ?? [])]?.includes(resData?.param?.base?.avatar)) {
      setAvatarUrl?.(resData?.param?.base?.avatar)
    } else {
      setAvatarUrl?.(resData?.avatar)
    }
    const allPhotos = [...(resData?.photos ?? []), ...(resData?.lifePhotosByMatchmaker ?? []), ...(resData?.photosByAI ?? [])];
    checkedPhotos.value = resData?.param?.base?.photos?.filter(x => allPhotos?.includes(x)) ?? []
    infoOrigin.value = resData?.param?.baseSource === 'app' ? '1' : '2'
  } else {
    checkedPhotos.value = [...(resData?.photos ?? []), ...(resData?.lifePhotosByMatchmaker ?? []), ...(resData?.photosByAI ?? [])];
    useVoice.value = !!resData?.voice?.url;
    useInterestTag.value = resData?.interestTags?.length !== 0;
    !avatarUrl.value && setAvatarUrl?.(resData?.avatar)
  }
};

getInfo(props?.userID);

let flag = false;
const submit = async () => {
  if (isServe?.value && !abouts.value?.recommendReason) {
    return message.error("请填写推荐原因");
  }
  if (flag) return;
  flag = true;

  try {
    const { data: res = {} } = await postV3MmRecommend({
      base: {
        userID: props?.userID,
        avatar: avatarUrl.value,
        description: useDesc?.value ? data.value?.description : undefined,
        photos: checkedPhotos.value?.length !== 0 ? checkedPhotos.value : undefined,
        ...submitInfo.value,
        source: infoOrigin.value === '1' ? 'app' : 'side-user'
      },
      ...abouts.value,
      useVoice: isServe?.value && useVoice.value,
      useInterestTag: isServe?.value && useInterestTag.value,
      markingTag: isServe?.value && useFeatureTag.value ? markingTag.value : undefined,
      source: isServe?.value ? 'serve' : 'sale',
      recommendType: props?.recommendType,
      recommenderUserID: props?.recommendType === 3 ? userInfo?.value?.id : props?.userID,
      recommendedUserID: props?.recommendType === 3 ? props?.uid : userInfo?.value?.id ? [userInfo?.value?.id] : undefined,
      action: props?.userID === userInfo?.value?.id ? "direct-to-passive" : undefined,
      isCustomRecommend: true
    })

    const { successNum = 0,
      failedNum = 0,
      failedList = [],
      failedReason = "" } = res;

    // 批量推荐的弹窗提示
    if (props?.recommendType === 3) {
      if (!failedNum) {
        message.success("发送成功");
        emits("update");
        emits("update:modelValue", false);
        return;
      }
      const failedPersons = failedList?.map(
        (item) => `${item.name}(${item.realName})`
      );
      ElMessageBox.alert(
        `成功发送${successNum}人。未成功${failedNum}人<br />未成功用户:${failedPersons.join(
          ","
        )}<br/>未成功原因：${failedReason}`,
        {
          type: "warning",
          dangerouslyUseHTMLString: true,
        }
      );
    } else {
      emits("update");
      message.success("成功");
    }

    emits("update:modelValue", false);
    refreshServiceLog?.();
    flag = false;
  } catch {
    flag = false;
  }
};

const close = () => {
  abouts.value = {};
  data.value = undefined;
  emits("update:modelValue", false);
};


</script>

<template>
  <ZoomDialog title="自定义推荐" fullScreen :model-value="modelValue"
    @update:model-value="(val) => emits('update:modelValue', val)" style="width: 50%"
    body-style="max-height: calc(100vh - 400px); overflow: auto;" draggable>
    <div class="top">
      <Avatar :avatar="data?.avatar" />
      <div class="reason">
        <p>昵称：{{ data?.nickName }}</p>
        <p>推荐原因：</p>
        <Textarea autosize style="width: 100%" v-model:value="abouts.recommendReason" />
      </div>
    </div>

    <!-- <FeatureTag v-if="isServe && !uid" :tag="markingTag">
      <Checkbox v-model:checked="useFeatureTag">特点标签</Checkbox>
    </FeatureTag> -->

    <MyVoice v-if="isServe && data?.voice?.url" :voice="data?.voice">
      <Checkbox v-model:checked="useVoice">我的声音</Checkbox>
    </MyVoice>



    <div class="custom-rec">
      <!-- <BaseInfo />
      <section>
        <Checkbox v-model:checked="useDesc">关于我</Checkbox>
        <Textarea class="desc" autosize disabled :value="data?.['description']"></Textarea>
      </section>

      <section v-if="data?.idCard">
        <div class="idcard">
          <div class="icon"></div>
          <div class="con">
            <h5>{{ data?.gender === 'female' ? '她' : '他' }}已完成实名、头像认证</h5>
            <p>身份证：{{ data?.idCard }},年龄属实</p>
            <p>头像是本人真实照片，已通过人脸比对</p>
          </div>
        </div>
      </section>

      <template v-if="userID">
        <Photos :rec-user-id="userID" :data="data" @update="getInfo(userID)" />
      </template>

<InterestTag v-if="isServe && data?.interestTags?.length !== 0" :tags="data?.interestTags">
  <Checkbox v-model:checked="useInterestTag">兴趣标签</Checkbox>
</InterestTag> -->


      <section>
        <h5>关于家庭</h5>
        <Textarea autosize v-model:value="abouts.aboutFamily"></Textarea>
      </section>
      <section>
        <h5>关于性格</h5>
        <Textarea autosize v-model:value="abouts.aboutCharacter"></Textarea>
      </section>
      <section>
        <h5>关于爱好</h5>
        <Textarea autosize v-model:value="abouts.aboutHobbies"></Textarea>
      </section>
    </div>

    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submit">确 认</el-button>
      </div>
    </template>
  </ZoomDialog>
</template>

<style scoped>
.custom-rec :deep(.ant-checkbox-wrapper) {
  display: flex;
  margin-top: 10px;
}

.custom-rec :deep(.ant-checkbox-wrapper) {
  margin: 8px 0;
}

section {
  margin-bottom: 10px;
}


.top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.reason {
  flex: 1;
}

.dialog-footer {
  padding: 10px 20px 20px;
  text-align: right;
  box-sizing: border-box;
}


.desc {
  color: rgba(0, 0, 0, 0.9) !important;
}

.idcard {
  width: 328px;
  padding: 20px 10px;
  display: flex;
  gap: 10px;
  background: linear-gradient(90deg, rgba(110, 185, 255, 0.5) 0%, rgba(79, 216, 255, 0.15) 100%);
  border-radius: 20px;
}

.idcard .icon {
  width: 30px;
  height: 30px;
  background: url('./assets/icon.png') no-repeat;
  background-size: cover;
}

.idcard .con {
  color: #141414;
  font-size: 13px;
  font-weight: 400;
}

.idcard h5 {
  color: #141414;
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  margin: 0;
  margin-bottom: 6px;
}

.idcard p {
  margin: 0;
  opacity: 0.4;
}
</style>