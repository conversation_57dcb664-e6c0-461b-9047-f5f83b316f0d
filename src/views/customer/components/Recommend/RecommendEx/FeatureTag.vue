<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Tag } from 'ant-design-vue'
import { getMarkingTag } from '../useMarkingTag'

// Props 定义
interface Props {
  userID?: number
  modelValue?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => []
})

// Emits 定义
const emits = defineEmits<{
  'selection-change': [selectedTags: string[]]
  'update:modelValue': [selectedTags: string[]]
}>()

// 获取标签数据源
const { markingTag } = getMarkingTag()

// 选中的标签状态
const selectedTags = ref<string[]>([...props.modelValue])

// 计算属性
const selectedCount = computed(() => selectedTags.value.length)
const totalCount = computed(() => markingTag.value.length)
const isValidSelection = computed(() => selectedCount.value >= 3)

// 监听 props.modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedTags.value = [...newValue]
  },
  { deep: true }
)

// 监听选中状态变化，触发事件
watch(
  selectedTags,
  (newValue) => {
    emits('selection-change', [...newValue])
    emits('update:modelValue', [...newValue])
  },
  { deep: true }
)

// 切换标签选择状态
const toggleTag = (tag: string) => {
  const index = selectedTags.value.indexOf(tag)
  
  if (index > -1) {
    // 取消选择
    selectedTags.value.splice(index, 1)
  } else {
    // 选择标签，但不能超过6个
    if (selectedTags.value.length < 6) {
      selectedTags.value.push(tag)
    }
  }
}

// 判断标签是否被选中
const isTagSelected = (tag: string) => {
  return selectedTags.value.includes(tag)
}
</script>

<template>
  <div class="feature-tag-container">
    <!-- 标题区域 -->
    <div class="header">
      <h4 class="title">
        特点标签 ({{ selectedCount }}/{{ totalCount }})
      </h4>
      <div class="tip-text">
        至少选择3个，小于3个不展示，至多选择6个
      </div>
    </div>

    <!-- 标签选择区域 -->
    <div class="tags-container">
      <Tag
        v-for="tag in markingTag"
        :key="tag"
        :color="isTagSelected(tag) ? '#1890ff' : undefined"
        :class="[
          'tag-item',
          {
            'tag-selected': isTagSelected(tag),
            'tag-unselected': !isTagSelected(tag)
          }
        ]"
        @click="toggleTag(tag)"
      >
        {{ tag }}
      </Tag>
    </div>

    <!-- 验证提示 -->
    <div v-if="!isValidSelection && selectedCount > 0" class="validation-tip">
      请至少选择3个标签
    </div>
  </div>
</template>

<style scoped>
.feature-tag-container {
  width: 100%;
}

.header {
  margin-bottom: 16px;
}

.title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.tip-text {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.5;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.3s ease;
  user-select: none;
}

.tag-selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.tag-unselected {
  background-color: #fff;
  border-color: #d9d9d9;
  color: #000;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
}

.tag-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.validation-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #ff4d4f;
}
</style>
