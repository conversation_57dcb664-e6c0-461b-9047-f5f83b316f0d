<script setup lang="ts">
import { ref, computed, h } from "vue";
import { compareVersion } from "@qianshou/common";
import CustomRecommend from "./CustomRecommend.vue";
import CustomRecommendEx from "./RecommendEx/CustomRecommendEx.vue";
import { GptRecommend } from "@qianshou/component";
import { useUserInfo, getRefreshServiceLog, getIsServe, getPreSaleRecommendInfo, postV3MmRecommend, hasServePool, alertData } from "@qianshou/service";
import { Button } from '@qianshou/ui'
import { ElNotification, ElForm, ElMessage, ElMessageBox, ElTooltip } from "element-plus";
import { Modal } from 'ant-design-vue';
import { validateKeywords } from "@qianshou/service";
import { postV2SideUserInfo } from "@qianshou/service"
import MultiRecommendDialog from "./MultiRecommendDialog.vue";
import CheckRecommend from "./CheckRecommend.vue";
import VerifyMemberInfoDialog from "./VerifyMemberInfoDialog.vue";
import { AIMatchAnalysisButton } from "@qianshou/component"
import { openDetailTab } from '@/utils/open'

const { form = {}, ruleFormRef, checkAlert, changeVisible, visible, hasOtherId, checkModalShow } = getPreSaleRecommendInfo() ?? {}

const { userInfo } = useUserInfo();
const isServe = getIsServe();
const emits = defineEmits(["update"]);
const recommendedUser = ref(Number(form?.otherUserId))

const refreshServiceLog = getRefreshServiceLog();
const showRecDialog = ref(false);
const showVerifyDialog = ref(false);
const showForbiddenDialog = ref(false);
const forbiddenReason = {
  notRecommendToPassive: '1. 未询问被动方是否喜欢会员',
  passiveNotFeedback: '1. 被动方未反馈是否喜欢会员',
  passiveInfoNotCompleted: '1. 被动方必填项未100%填写',
  passiveRejected: '1. 被动方不喜欢会员，不可推荐'
}

const moderationSum = ref<string>()
const handleUpdate = (sum: string) => {
  showRecDialog.value = false;
  if (hasServePool('PrimeMoverMatchmaker') || hasServePool('GoBetweenMatchmaker')) {
    showVerifyDialog.value = true;
  } else {
    showCustomRecommend.value = true
  }
  isDirect.value = false
  moderationSum.value = sum
  recommendedUser.value = Number(userInfo?.value?.id)
}


const validate = async () => {
  if (!form?.otherUserId || !String(form?.otherUserId)?.trim()) {
    ElMessage.error({
      showClose: true,
      message: "请输入推荐人",
    });
    return false
  }
  if (Number(form?.otherUserId) === userInfo?.value?.id) {
    ElMessage.error({
      showClose: true,
      message: "推荐人不能是当前用户",
    });
    return false
  }
  if (isServe?.value && (!form.reason)) {
    ElMessage.error({
      showClose: true,
      message: "请填写推荐语",
    });
    return false
  }

  const res = await validateKeywords(form.reason)
  if (!res) return false;
  return true
}
const isDirect = ref(true)

const reverseRec = () => {
  changeVisible?.(false);
  showForbiddenDialog.value = false;
  ElMessageBox.close();
  showRecDialog.value = true;
}

const check = async (type: 'direct' | 'edit') => {
  if (!await validate()) return;

  isDirect.value = type === 'direct'
  recommendedUser.value = Number(form?.otherUserId)

  if ((hasServePool('PrimeMoverMatchmaker') || hasServePool('GoBetweenMatchmaker')) && !userInfo?.value?.isMembership && alertData.value?.recommenderUser?.inServe) {
    changeVisible?.(false);
    showVerifyDialog.value = true
    return
  }

  if (isServe?.value && !alertData?.value?.direct) {
    // 检查是否禁止推荐，替换原来的TODO逻辑
    if (alertData.value?.nextStage && ['notRecommendToPassive', 'passiveNotFeedback', 'passiveInfoNotCompleted', 'passiveRejected'].includes(alertData.value?.nextStage)) {
      showForbiddenDialog.value = true;
      return;
    } else if (alertData.value?.nextStage === 'appPassiveNotFeedback') {
      ElMessageBox.confirm("", "提示", {
        message: h('div', { class: 'msg' }, [
          h('p', { class: 'tip' }, '由于推荐的用户不是在期会员、被动方或喜欢会员的人。请先将会员介绍给被动方。（被动方喜欢会员后，系统会自动推荐给会员）'),
          h('p', { class: 'button-line' }, [
            h(Button, { type: 'default', class: 'tooltip-btn', onClick: () => { ElMessageBox.close(); type === 'direct' ? goRecommend() : goRecommendCard() } }, { default: () => '不用了，仍要推荐' }),
            h(Button, { type: 'primary', onClick: reverseRec }, { default: () => '好的，先去介绍' }),
          ])
        ]),
        showCancelButton: false,
        showConfirmButton: false,
      });
    }
  } else {
    type === 'direct' ? goRecommend() : goRecommendCard()
  }
}

const goNext = () => {
  showVerifyDialog.value = false;
  isDirect.value ? goRecommend() : goRecommendCard()
}

const goOtherDetail = () => {
  showForbiddenDialog.value = false;
  changeVisible?.(false);
  openDetailTab(String(alertData.value?.recommenderUser?.id))
}

const goRecommend = async () => {
  if (!await validate()) return;
  ruleFormRef?.value?.validate(async (valid) => {
    if (!valid) {
      return false;
    }
    try {
      await postV3MmRecommend({
        recommendedUserID: [userInfo?.value?.id as number],
        recommenderUserID: Number(form?.otherUserId),
        recommendType: recommendedUser.value === userInfo?.value?.id ? 3 : isServe?.value ? 2 : 1,
        source: isServe?.value ? 'serve' : 'sale',
        recommendReason: form?.reason,
        saveRecommendReason: isServe?.value && form?.reason && !alertData?.value?.moderationSum && form?.save ? true : false,
      });
      ElNotification.success("操作成功");
      changeVisible?.(false);
      emits("update");
      refreshServiceLog?.();
    } catch { }
  });
};

// 自定义弹窗展示
const showCustomRecommend = ref(false);
const goRecommendCard = async () => {

  if (!await validate()) return;
  ruleFormRef?.value?.validate(async (valid) => {
    if (!valid) {
      return false;
    }
    try {
      if (form?.reason && !alertData.value?.moderationSum && form.save) {
        await postV2SideUserInfo({
          userId: Number(form.otherUserId),
          tag: 'moderationSum',
          moderationSum: form?.reason
        })
      }
    } catch { }
    showCustomRecommend.value = true;
    changeVisible?.(false);
  });
};


const rules = {
  otherUserId: [
    { required: true, message: "请填写被推荐人ID", trigger: "blur" },
  ],
  reason: [{ ax: 300, message: "最少20个字最多300个字", trigger: "blur" }],
};


// 版本号低于2.8.26的提示 禁用自定义推荐
const showTips = computed(() => {
  const appVersion = userInfo?.value?.info?.appVersion?.split("|");
  const version = appVersion?.[appVersion?.length - 1];
  console.log("version  ", version);
  if (!compareVersion(version, "2.8.26")) {
    return true;
  } else {
    return false;
  }
});
</script>
<template>
  <el-dialog append-to-body title="推荐" v-model="visible" :close-on-click-modal="false" width="40%">
    <el-form ref="ruleFormRef" style="position: relative;" v-if="form" :model="form" :rules="rules" label-width="100px">
      <el-alert v-if="alertData?.level" :title="alertData.level === 'success' ? '' : '提示'" :type="alertData?.level"
        :description="alertData?.alert?.join(',')" show-icon :closable="false" />
      <el-form-item label="给当前用户" prop="userId">
        <el-input v-if="userInfo" style="width: 200px" v-model="userInfo.id" type="text" :disabled="true" />
        &nbsp;&nbsp;({{
          `${userInfo?.info?.gender === "female" ? "女" : "男"},${userInfo?.info?.name
          },${userInfo?.info?.realName?.charAt(0)}-${userInfo?.isMembership ? '会员' : '非会员'}`
        }})
      </el-form-item>
      <el-form-item label="推荐" prop="otherUserId">
        <el-input style="width: 200px" v-model.trim="form.otherUserId" :disabled="hasOtherId" @change="(val: string) => {
          form.otherUserId = val ? Number(val) : undefined;
          form.reason = undefined;
          checkAlert?.();
        }" type="text" placeholder="被推荐人ID" />
        <span v-if="alertData?.recommenderUser?.name || alertData?.recommenderUser?.nickName">
          &nbsp;&nbsp;({{
            `${alertData?.recommenderUser?.gender === "female" ? "女" : "男"},${alertData?.recommenderUser?.nickName
            },${alertData?.recommenderUser?.name?.charAt(0)}-${alertData?.recommenderUser?.inServe ? '会员' : '非会员'} `
          }})
        </span>
      </el-form-item>

      <template v-if="isServe">
        <el-form-item label="推荐理由" prop="reason">
          <GptRecommend v-model="form.reason" placeholder="请填写推荐理由" :userId="Number(form.otherUserId)"
            :isImmediate="!!form.otherUserId" @update="(val) => (form.reason = val)" />
        </el-form-item>
        <el-form-item v-if="form.reason && !alertData.moderationSum">
          <el-checkbox v-model="form.save">保存为红娘推荐语</el-checkbox>
        </el-form-item>
      </template>
      <el-form-item v-else label="推荐理由" prop="reason">
        <el-input type="textarea" placeholder="请填写推荐理由" size="small" v-model="form.reason"></el-input>
      </el-form-item>
      <AIMatchAnalysisButton class="ai-match-analysis-button" :otherUserId="Number(form.otherUserId)" />
    </el-form>
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="changeVisible?.(false)">取 消</el-button>
        <el-button :disabled="alertData?.level === 'error'" type="primary" @click="check('direct')">直接推荐</el-button>
        <el-tooltip v-if="showTips" class="item" effect="dark" :content="'用户版本过低，无法使用该功能'" placement="top">
          <el-button type="success" style="opacity: 0.4">编辑后推荐</el-button>
        </el-tooltip>
        <el-button v-else type="success" @click="check('edit')"
          :disabled="alertData?.level === 'error'">编辑后推荐</el-button>
      </div>
    </template>
  </el-dialog>

  <MultiRecommendDialog v-if="showRecDialog" v-model="showRecDialog" @close="() => showRecDialog = false"
    source="direct-to-passive" @update="handleUpdate" :uid="form.otherUserId ? [form.otherUserId] : undefined"
    :alertData="alertData" />

  <!-- <CustomRecommend class="pre-sale-rec" v-if="showCustomRecommend" v-model="showCustomRecommend"
    :userID="recommendedUser"
    :uid="recommendedUser === userInfo?.id && form.otherUserId ? [Number(form.otherUserId)] : undefined"
    @update="() => emits('update')" :reason="moderationSum ?? form?.reason"
    :recommendType="recommendedUser === userInfo?.id ? 3 : isServe ? 2 : 1" /> -->


  <CustomRecommendEx class="pre-sale-rec" v-if="showCustomRecommend" v-model="showCustomRecommend"
    :userID="recommendedUser"
    :uid="recommendedUser === userInfo?.id && form.otherUserId ? [Number(form.otherUserId)] : undefined"
    @update="() => emits('update')" :reason="moderationSum ?? form?.reason"
    :recommendType="recommendedUser === userInfo?.id ? 3 : isServe ? 2 : 1" />


  <CheckRecommend v-if="checkModalShow" />

  <VerifyMemberInfoDialog
    :user="alertData?.recommenderUser?.inServe ? alertData?.recommenderUser : alertData?.recommendedUser"
    :passiveUserID="alertData?.recommenderUser?.inServe ? [alertData?.recommendedUser?.id as number] : [alertData?.recommenderUser?.id as number]"
    v-if="showVerifyDialog" v-model:visible="showVerifyDialog" @update="goNext" />

  <!-- 禁止推荐弹窗 -->
  <Modal v-model:visible="showForbiddenDialog" title="禁止推荐" :zIndex="20000" :width="500" :maskClosable="false"
    :footer="null" class="forbidden-dialog">
    <div class="forbidden-content">
      <p class="forbidden-title">禁止推荐原因如下：</p>
      <div class="forbidden-reasons">
        {{ alertData?.nextStage ? forbiddenReason[alertData?.nextStage] : '' }}
      </div>
    </div>
    <div class="forbidden-footer">
      <Button v-if="alertData?.nextStage === 'notRecommendToPassive'" type="primary" @click="reverseRec">先去介绍会员</Button>
      <Button v-if="alertData?.nextStage === 'passiveNotFeedback'" type="primary"
        @click="goOtherDetail">去跟进被动方反馈</Button>
      <Button v-if="alertData?.nextStage === 'passiveInfoNotCompleted'" type="primary"
        @click="goOtherDetail">去编辑被动方资料</Button>

    </div>
  </Modal>
</template>
<style scoped>
.pre-sale-rec {
  z-index: 9999 !important;
}
</style>
<style>
.msg .button-line {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 80px;
}

.msg .tip {
  margin-bottom: 20px;
}

.tooltip-btn {
  position: relative
}

.tooltip-btn ::after {
  content: '系统标记为盲推测眼缘';
  position: absolute;
  padding: 8px 20px;
  border-radius: 5px;
  background: #303133;
  color: #fff;
  top: -50px;
  left: -20px;
  font-size: 12px;

}

.tooltip-btn ::before {
  content: '';
  width: 12px;
  height: 12px;
  background: #303133;
  display: block;
  transform: rotate(45deg);
  left: 50px;
  border-radius: 2px;
  top: -22px;
  position: absolute;
}

.ai-match-analysis-button {
  position: absolute;
  right: 0;
  bottom: -20px;
}

/* 禁止推荐弹窗样式 */
:deep(.forbidden-dialog .ant-modal-title) {
  font-weight: 600;
  color: #303133;
}

:deep(.forbidden-dialog .ant-modal-body) {
  padding: 20px 30px;
}

.forbidden-content {
  padding: 10px 0;
}

.forbidden-title {
  font-weight: 500;
  margin-bottom: 15px;
  color: #303133;
}

.forbidden-reasons {
  padding-left: 10px;
}

.reason-item {
  margin-bottom: 10px;
}

.reason-item p {
  line-height: 1.6;
  margin: 0;
  color: #606266;
}

.forbidden-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>