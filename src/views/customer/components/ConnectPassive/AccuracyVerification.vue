<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Button, Modal } from 'ant-design-vue';
import { postV3CommunicationQualityEvaluation, type PostV3CommunicationQualityEvaluationResponse, postV3PassiveContactNote } from '@qianshou/service'

interface Props {
  passiveId?: number;
  userId?: number;
  meetingInfo?: any[];
  specialInfo?: any[];
  basicInfo?: any[];
  allInfos?: any[];
  accuracyRate?: number
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'accuracyChange', value: number | undefined): void;
}>();


const loading = ref(false);
const modalVisible = ref(false);

const accuracyData = ref<Required<PostV3CommunicationQualityEvaluationResponse>['data']['passiveContact']>();

// 判断是否需要检查字段
const needCheck = (x: any) => {
  // 如果没有hideDepends或者hideDepends条件不满足，则需要检查
  if (!x?.hideDepends?.length) return true;

  // 检查是否有任何hideDepends条件满足（如果满足则不需要检查）
  return !x.hideDepends.some((y: any) =>
    y.value?.includes(props.allInfos?.find((z: any) => z.name === y.key)?.value)
  );
};

// 提交表单数据，只提交base部分，不做校验
const submitFormData = async () => {
  if (!props.passiveId || !props.userId || !props.allInfos) return;

  try {
    // 清空所有不需要检查的字段（被hideDepends隐藏的字段）
    const processedInfos = [...props.allInfos];
    processedInfos.forEach(x => {
      if (!needCheck(x)) {
        x.value = undefined;
        x.checked = false;
      }
    });

    // 构建提交数据，只包含base部分
    const obj = processedInfos.reduce((acc, item) => {
      acc[item.name] = { value: item.value, checked: item.checked };
      return acc;
    }, {});

    // 提交数据到服务器，只提交base部分
    await postV3PassiveContactNote({
      passiveID: props.passiveId,
      userID: props.userId,
      base: obj,
      source: 'pre-quality-evaluation' // 标记为质检前保存，只传base部分
    });

    console.log('表单数据提交成功');
  } catch (error) {
    console.error('表单数据提交失败:', error);
  }
};

const handleVerification = async () => {
  if (!props.passiveId || !props.userId) return;

  try {
    loading.value = true;

    // 先提交表单数据
    await submitFormData();

    // 然后进行验证
    const { data } = await postV3CommunicationQualityEvaluation({
      extra: {
        memberUserID: props.userId, //被动方牵线时传会员id
      },
      userID: props.passiveId, // 被动方的id
      businessType: 'passive-contact',
      materialType: ['phone', 'we-com-voice-call', 'we-com-msg'],
    }, {
      timeout: 60 * 1000
    })
    accuracyData.value = data?.passiveContact
    emit('accuracyChange', data?.passiveContact?.accuracyRate);

    modalVisible.value = true;
  } catch (error) {
    console.error('验证失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleModalClose = () => {
  modalVisible.value = false;
};

// 获取准确度状态的颜色
const getAccuracyStatusColor = (accuracy?: number) => {
  if (!accuracy) return '#d9d9d9';
  if (accuracy >= 0.8) return '#52c41a';
  if (accuracy >= 0.6) return '#faad14';
  return '#f5222d';
};

watch([props.meetingInfo, props.specialInfo, props.basicInfo, props.allInfos], () => {
  accuracyData.value = undefined;
  emit('accuracyChange', undefined);
}, { deep: true })
</script>

<template>
  <div class="accuracy-verification">
    <h4>准确度：
      <span class="accuracy-value" :style="{ color: getAccuracyStatusColor(accuracyRate) }">
        {{ `${accuracyRate ? (accuracyRate * 100).toFixed(2) : '--'}%` }}
      </span>

      <Button type="primary" size="small" :loading="loading" @click="handleVerification" class="verify-button">
        <template v-if="loading">
          <span class="loading-text">生成中</span>
          <span class="loading-dots">...</span>
        </template>
        <template v-else>验证</template>
      </Button>
    </h4>
    <Modal title="填写信息准确度分析" v-model:visible="modalVisible" @cancel="handleModalClose" :footer="null" width="480px"
      :zIndex="11000">
      <div class="result-content">
        <h4>已填写的准确度：
          <span class="accuracy-value" :style="{ color: getAccuracyStatusColor(accuracyData?.accuracyRate) }">
            {{ `${accuracyData?.accuracyRate ? (accuracyData?.accuracyRate * 100).toFixed(2) : '--'}%` }}
          </span>
          <span v-if="accuracyData?.qualityIssues?.length">有
            <span :style="{ color: getAccuracyStatusColor(accuracyData?.accuracyRate) }">{{
              accuracyData?.qualityIssues?.length }}</span>项信息不准确</span>
        </h4>
        <div class="result-item" v-for="(item, index) in accuracyData?.qualityIssues" :key="item.label">
          <span class="result-label">{{ index + 1 }}.{{ item.label }}:</span>
          <span class="result-value">{{ item?.value }}</span>
          <span class="result-reason">{{ item?.reason }}</span>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.accuracy-verification {
  margin-top: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
}

.verification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.accuracy-info {
  display: flex;
  align-items: center;
}

.accuracy-status {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.accuracy-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.accuracy-value {
  margin-right: 5px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
}

.verify-button {
  min-width: 80px;
}

.loading-text {
  display: inline-block;
  margin-right: 2px;
}

.loading-dots {
  display: inline-block;
  animation: loadingDots 1.4s infinite;
  width: 16px;
}

@keyframes loadingDots {
  0% {
    content: '.';
  }

  33% {
    content: '..';
  }

  66% {
    content: '...';
  }

  100% {
    content: '.';
  }
}

.result-content {
  padding: 16px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 24px;
  padding-bottom: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.accuracy-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.result-item {
  margin-top: 12px;
  display: flex;
  align-items: flex-start;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
  flex-shrink: 0;
}

.result-value {
  min-width: 100px;
  color: #666;
}

.suggestion {
  color: #1890ff;
  font-weight: 500;
}

.inaccurate-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.inaccurate-tag {
  display: inline-block;
  padding: 2px 8px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 12px;
}
</style>