<script setup lang="ts">
/**
 * 确认二见, 记录二见结果
 * 基于ReportMeeting.vue组件，修改为二见相关
 */

import { reactive, ref } from 'vue'
import { ZoomDialog, getPopupContainer, DateTimePicker, Button } from '@qianshou/ui'
import { Form, FormItem, Input, RadioGroup, Textarea, Card, type FormInstance, message } from 'ant-design-vue'
import { joinComma } from '@qianshou/common';
import { useUser, setAchievement, config, type PostV3AchievementRequest } from '@qianshou/service';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

const emits = defineEmits(['update'])

const visible = ref(false)

type Meeting = Required<Required<PostV3AchievementRequest>['meet']>['otherFeedback']

interface User {
  id?: number;
  name?: string;
  realName?: string;
  gender?: number;
}

type reportTypeEnum = 'member' | 'other' | undefined

const form = reactive<{
  time?: Dayjs;
  source?: string;
  memberFeedback: Meeting;
  otherFeedback: Meeting;
}>({
  memberFeedback: {},
  otherFeedback: {}
})

const otherUser = ref<User>()
const user = ref<User>()
const reportType = ref<reportTypeEnum>()

const userInfo = useUser();

function hide() {
  visible.value = false
}

// 重置表单使用useForm控制form状态对象. 用Form控件存在无法初始化动态增减的数组状态
const { resetFields, } = Form.useForm(form)
/** 打开窗口并初始化数据 */
async function show(params?: User, outUser?: User, type?: reportTypeEnum, contactStatus?: number) {
  visible.value = true
  resetFields?.()
  form.time = dayjs()
  otherUser.value = params;
  reportType.value = type
  if (contactStatus && type === 'member') {
    form.memberFeedback.contactStatus = contactStatus
  }
  if (contactStatus && type === 'other') {
    form.otherFeedback.contactStatus = contactStatus
  }
  if (outUser?.id) {
    user.value = outUser
  } else {
    user.value = {
      ...userInfo?.value,
      gender: userInfo?.value?.info?.gender === 'male' ? 1 : 2,
      realName: userInfo?.value?.info?.realName,
      name: userInfo?.value?.info?.name,
    }
  }
}

const formRef = ref<FormInstance>()
const onSubmit = async () => {
  try {
    await formRef.value?.validate();
    await setAchievement({
      userId: user?.value?.id,
      otherUserId: otherUser?.value?.id,
      serviceType: reportType.value ? 'schedule-meet-second-edit' : "schedule-meet-second",
      serviceTime: form.time?.valueOf(),
      meet: {
        otherFeedback: Object.keys(form.otherFeedback)?.length ? form.otherFeedback : undefined,
        memberFeedback: Object.keys(form.memberFeedback)?.length ? form.memberFeedback : undefined
      },
      setUpMeeting: !reportType.value ? {
        source: form?.source,
      } : undefined
    })

    message.success(`${reportType.value ? '修改成功' : '确认二见成功'}`);

    hide()
    emits('update')

  } catch (e: any) {
    if (!e.errorFields?.length) {
      message.error(`${reportType.value ? '修改失败' : '确认二见失败'}`);
    }
  }
};

const layout = {
  labelCol: { span: 7, },
  wrapperCol: { span: 17 },
};

defineExpose({ show })

</script>
<template>
  <ZoomDialog :title="!reportType ? '确认二见' : '二见印象'" v-model="visible" :width="760" draggable
    body-style="max-height: calc(100vh - 330px); overflow: auto;">
    <Form ref="formRef" :model="form" name="form" v-bind="layout" validateOnRuleChange scrollToFirstError :colon="false"
      :validateTrigger="['blur', 'change']" labelWrap>
      <div class="content">
        <FormItem label="见面对象">
          <div>
            <Input disabled :value="user?.id" size="small" style="width: 100px;" />
            ({{ joinComma(user?.gender === 2 ? '女' : '男', user?.name, user?.realName) }})
          </div>

          <div style="margin-top: 10px;" v-if="otherUser">
            <Input disabled :value="otherUser?.id" size="small" style="width: 100px;" />
            ({{ joinComma(otherUser?.gender === 2 ? '女' : otherUser?.gender === 1 ? '男' : '其它',
              otherUser?.name, otherUser?.realName) }})
          </div>
        </FormItem>

        <template v-if="!reportType">
          <FormItem name="time" required label="二见时间">
            <DateTimePicker v-model="form.time" :getPopupContainer="getPopupContainer" go="forward" />
          </FormItem>

          <FormItem label="见面来源" required :name="['source']">
            <RadioGroup v-model:value="form.source" style="width: 100%" :options="config?.meetingEnum?.source" />
          </FormItem>
        </template>

        <Card v-if="!reportType || reportType === 'member'" :title="`${user?.realName}对${otherUser?.realName}的二见印象:`"
          size="small" :bordered="false">

          <FormItem :required="!!reportType" :name="['memberFeedback', 'contactStatus']" label="是否继续接触" v-bind="layout">
            <RadioGroup v-model:value="form.memberFeedback.contactStatus"
              :options="config?.selectionEnum?.secondMeetContactStatus" />
          </FormItem>
          <FormItem name="additionalNote" label="具体印象" v-bind="layout">
            <Textarea :autoSize="{ minRows: 4 }" v-model:value="form.memberFeedback.additionalNote" />
          </FormItem>
        </Card>

        <Card v-if="!reportType || reportType === 'other'" :title="`${otherUser?.realName}对${user?.realName}的二见印象:`"
          size="small" :bordered="false">
          <FormItem :required="!!reportType" :name="['otherFeedback', 'contactStatus']" label="是否继续接触" v-bind="layout">
            <RadioGroup v-model:value="form.otherFeedback.contactStatus"
              :options="config?.selectionEnum?.secondMeetContactStatus" />
          </FormItem>
          <FormItem name="additionalNote" label="具体印象" v-bind="layout">
            <Textarea :autoSize="{ minRows: 4 }" v-model:value="form.otherFeedback.additionalNote" />
          </FormItem>
        </Card>
      </div>
    </Form>

    <template #footer>
      <div class="footer">
        <Button type="default" @click="hide">取 消</Button>
        <Button @click.prevent="onSubmit">确 定</Button>
      </div>
    </template>
  </ZoomDialog>
</template>

<style scoped>
.content {
  position: relative;
}

.content :deep(.ant-form-item) {
  margin-bottom: 5px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 5px;
}
</style>
