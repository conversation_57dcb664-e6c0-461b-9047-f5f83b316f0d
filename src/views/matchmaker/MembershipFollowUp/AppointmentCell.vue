<script setup lang="ts">
/**
 * 预约一见单元格组件
 * 用于展示预约一见的状态和操作
 */
import { Button, message, Tooltip } from 'ant-design-vue';
import { ElMessageBox } from 'element-plus';
import { DeleteOutlined, EditOutlined, ClockCircleOutlined } from '@ant-design/icons-vue';
import { adminRights, postV3Achievement } from '@qianshou/service';
import { dayjsMinutes } from '@qianshou/common';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord;
}>();

const emit = defineEmits(['openAppointment', 'update']);

// 打开预约一见弹窗
const openAppointment = () => {
  emit('openAppointment', props.record);
};

// 撤销预约一见
const cancelAppointment = async () => {
  try {
    // 确定用户ID和被动方ID
    const userId = props.record.userInfo?.userID;
    const passiveId = props.record.passiveInfo?.userID;

    if (!userId || !passiveId) {
      message.error('缺少必要的用户信息');
      return;
    }

    // 显示确认弹窗
    await ElMessageBox.confirm(
      "是否取消本次见面预约？",
      "取消预约",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    // 调用接口撤销预约一见
    await postV3Achievement({
      userId: userId,
      otherUserId: passiveId,
      serviceType: "revoke-setUpMeeting"
    });

    message.success('撤销预约一见成功');
    emit('update');
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error !== 'cancel') {
      console.error('撤销预约一见失败:', error);
      message.error('撤销预约一见失败');
    }
  }
};
</script>

<template>
  <div>
    <!-- 如果没有预约时间且没有一见时间 -->
    <template v-if="!record.appointMeetTime && !record.meetTime">
      <!-- 如果被动方已提供时间 -->
      <span v-if="record.isPassiveProvideMeetTime">
        <Tooltip title="被动方已提供见面时间">
          <ClockCircleOutlined style="color: #1890ff; margin-right: 4px; cursor: help;" />
        </Tooltip>
        <Button type="link" size="small" @click="openAppointment" style="padding: 0 4px">
          预约一见
          <EditOutlined />
        </Button>
      </span>
      <!-- 显示预约一见按钮 -->
      <span v-else>
        <Button type="link" size="small" @click="openAppointment" style="padding: 0 4px">
          预约一见
          <EditOutlined />
        </Button>
      </span>
    </template>

    <!-- 如果已经一见 -->
    <span v-else-if="record.meetTime">已一见</span>

    <!-- 如果有预约时间 -->
    <span v-else-if="record.appointMeetTime">
      <span class="appointment-tag">预</span>
      {{ dayjsMinutes(record.appointMeetTime) }}
      <Button type="link" size="small"
        @click="cancelAppointment" style="padding: 0 4px" title="撤销预约">
        <DeleteOutlined style="color: #f50" />
      </Button>
    </span>
  </div>
</template>

<style scoped>
.appointment-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 8px;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
}
</style>
