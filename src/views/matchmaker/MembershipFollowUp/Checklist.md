# 会员跟进功能实现清单

## 基础结构
- [x] 创建设计文档
- [x] 创建实现清单
- [x] 完善现有组件结构

## 数据展示
- [x] 会员ID和被动方ID展示与跳转
- [x] 会员姓名和被动方姓名展示（性别+脱敏真实姓名+昵称）
- [x] 当前进展展示（包括微信图标）
- [x] 当前进展停留时间展示
- [x] 推给被动方时间展示
- [x] 被动方反馈（推卡）展示与编辑
- [x] 牵线进展展示
- [x] 推给会员时间展示
- [x] 会员反馈（推卡）展示与编辑
- [x] 预约一见状态展示与操作
- [x] 一见状态展示与操作
  - [x] 使用 ReportMeeting 组件实现确认一见功能
  - [x] 支持记录见面时间、见面来源和双方的见面印象
  - [x] 已移除旧版的 ConfirmMeet 组件，完全使用 ReportMeeting 组件
  - [x] 使用 CancelMeetConfirm 组件实现撤销一见功能
  - [x] 撤销一见确认弹窗使用模板实现，而非 Modal.confirm
  - [x] 将一见单元格渲染改为使用MeetCell组件，使用模板渲染而非JSX/h函数
  - [x] 优化MeetCell组件，根据权限显示不同内容：研发+质检权限可看到确认一见按钮和编辑图标，其他权限只能查看
  - [x] 将MeetCell组件中的撤销按钮改为使用DeleteOutlined图标，保持与其他组件的一致性
  - [x] 将确认一见和撤销一见的逻辑直接放在MeetCell组件内部实现，不再依赖父组件
  - [x] 修改撤销一见弹窗标题为"撤销见面"，提示文案为"是否撤销本次见面录入？"
- [x] 一见被动方反馈展示
- [x] 一见会员反馈展示
- [x] 交往状态展示与操作
  - [x] 用户处于交往关系中：显示开始时间+天数+删除按钮
  - [x] 用户不在交往关系中且没有婚姻/恋爱关系，但有历史交往记录：显示时间+已解除标签+编辑按钮
  - [x] 用户不在交往关系中但有婚姻/恋爱关系：显示时间+编辑按钮
  - [x] 用户从未有过交往关系：显示确认交往按钮
- [x] 恋爱状态展示与操作
  - [x] 状态1: isInLove=true, isUserConfirmLove=true - 显示恋爱时间+天数+已确认标签+编辑和删除按钮
  - [x] 状态2: isInLove=true, isUserConfirmLove=false - 显示恋爱时间+天数+未确认标签+编辑和删除按钮
  - [x] 状态3: isInLove=false, isInMarriage=false, 有lastLoveStartTime - 显示时间+已解除标签+编辑按钮
  - [x] 状态4: isInLove=false, 无lastLoveStartTime - 显示确认恋爱按钮
- [x] 结婚状态展示与操作（使用 MarriageRelationshipCell 组件实现）
  - [x] 状态1: isInMarriage=true - 显示结婚时间+删除按钮
  - [x] 状态2: isInMarriage=false, 有lastMarriageStartTime - 显示时间+已解除标签+编辑按钮
  - [x] 状态4: isInMarriage=false, 无lastMarriageStartTime - 显示确认结婚按钮+编辑图标
  - [x] 确认结婚功能：点击确认结婚按钮或编辑按钮打开 ReportMarried 组件，需要上传结婚证
  - [x] 解除结婚关系功能：点击删除按钮弹出确认对话框（使用模板实现），确认后调用 setAchievement 接口解除结婚关系
- [x] 跟进状态展示与切换

## 交互功能
- [x] 表格排序功能（当前进展、当前进展停留时间、推荐给会员时间）
- [x] 双击ID跳转到用户详情页
- [x] 点击姓名查看真实姓名
- [x] 预约一见弹窗功能
- [x] 确认一见功能
- [x] 反馈拒绝原因弹窗功能
- [x] 跟进状态切换功能
- [x] 更多操作下拉菜单（契合分析、视频约会、微信录入等）
  - [x] 将契合分析按钮逻辑单独提取到AnalysisAction组件中，实现功能模块化
  - [x] 将视频约会按钮逻辑单独提取到VideoDateAction组件中，实现功能模块化
  - [x] 创建useVideoDate hooks，使用provide/inject模式实现视频约会功能
  - [x] 在MembershipFollowUp组件中添加MakeDate组件实例，保持单一实例
  - [x] 根据achievement字段判断是否已视频约会，显示不同状态的按钮
  - [x] 将微信操作逻辑单独提取到WechatAction组件中，实现功能模块化
  - [x] 创建useWechatExchange hooks，使用provide/inject模式实现微信交换功能
  - [x] 根据achievement字段判断是否已交换微信，显示不同状态的按钮
  - [x] 创建WechatInfoModal组件，实现信息不完整时的提示弹窗
  - [x] 使用defineModel实现visible属性的双向绑定
  - [x] 支持Ref类型的属性，使用计算属性处理
  - [x] 根据userInfo.canExchangeWechat和passiveInfo.canExchangeWechat判断信息是否完整
  - [x] 显示userInfo.notCompleteFields和passiveInfo.notCompleteFields字段
  - [x] 与精选列表的微信交换提示弹窗保持一致
- [x] 表格所有单元格内容居中显示

## 权限控制
- [x] 按照用户角色控制可见内容和操作权限
- [x] 研发、质检（红娘用）等角色的特殊权限处理

## 测试与优化
- [x] 功能测试
- [x] 性能优化
- [x] 代码审查
- [x] 文档完善
