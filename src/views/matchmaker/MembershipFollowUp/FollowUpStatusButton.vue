<script setup lang="ts">
import { ref } from 'vue';
import { Button, message } from 'ant-design-vue';
import { ElMessageBox } from 'element-plus';
import { postV3MembershipFollowUpStatus } from '@qianshou/service';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord;
}>();

const emit = defineEmits(['update']);

const loading = ref(false);

const handleClick = async () => {
  const isFollow = props.record.followUpStatus === 0;
  const nextStatus = isFollow ? -1 : 0;
  const statusText = nextStatus === 0 ? '重新跟进' : '放弃跟进';
  try {
    await ElMessageBox.confirm(
      `是否${statusText}会员${props.record.userInfo?.userID}和被动方${props.record.passiveInfo?.userID}的进展？`,
      `确认${statusText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    loading.value = true;
    await postV3MembershipFollowUpStatus({
      userID: props.record.userInfo?.userID,
      passiveID: props.record.passiveInfo?.userID,
      followUpStatus: nextStatus
    });
    message.success(`已${statusText}`);
    emit('update');
  } catch (error) {
    // 用户取消不提示
    if (error !== 'cancel') {
      message.error('操作失败，请重试');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Button type="link" :loading="loading" @click="handleClick">
    {{ record.followUpStatus === 0 ? '放弃跟进' : '重新跟进' }}
  </Button>
</template>