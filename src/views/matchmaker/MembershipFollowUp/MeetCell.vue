<template>
  <div class="meet-cell">
    <!-- 有权限用户（研发+质检）的显示逻辑 -->
    <template v-if="hasPermission">
      <!-- 有一见时间：显示时间和撤销按钮 -->
      <template v-if="record.meetTime">
        <span>{{ dayjsMinutes(record.meetTime) }}</span>
        <Button
          type="link"
          size="small"
          @click.stop="handleCancelMeet"
          style="margin-left: 4px"
          title="撤销"
        >
          <DeleteOutlined style="color: #f50" />
        </Button>
      </template>

      <!-- 没有一见时间：显示确认一见按钮 -->
      <template v-else>
        <Button
          type="link"
          size="small"
          @click.stop="handleOpenConfirmMeet"
          style="margin-left: 4px"
        >
         确认一见
          <EditOutlined />
        </Button>
      </template>
    </template>

    <!-- 无权限用户的显示逻辑 -->
    <template v-else>
      <!-- 有一见时间：只显示时间 -->
      <template v-if="record.meetTime">
        {{ dayjsMinutes(record.meetTime) }}
      </template>

      <!-- 没有一见时间：显示占位符 -->
      <template v-else>
        -
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
/**
 * 一见单元格组件
 * 用于展示一见状态和操作
 *
 * 权限逻辑：
 * 1. 研发+质检（红娘用）权限：
 *    - 有一见时间：显示时间和撤销按钮
 *    - 没有一见时间：显示确认一见按钮和编辑图标
 *
 * 2. 其他权限：
 *    - 有一见时间：只显示时间
 *    - 没有一见时间：显示占位符"-"
 */
import { Button } from 'ant-design-vue';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { adminRights } from '@qianshou/service';
import { dayjsMinutes } from '@qianshou/common';
import type { TableRecord } from './types';
import { computed } from 'vue';
import { useMeetActions } from './hooks/useMeetActions';

const props = defineProps<{
  record: TableRecord;
}>();

// 使用一见相关操作的hooks
const meetActions = useMeetActions();

// 判断是否有权限（研发+质检）
const hasPermission = computed(() => {
  return adminRights.isDeveloperOrAdmin || adminRights.isQualityControl;
});

// 打开确认一见弹窗
const handleOpenConfirmMeet = () => {
  meetActions.openConfirmMeet(props.record);
};

// 打开撤销一见确认弹窗
const handleCancelMeet = () => {
  meetActions.openCancelMeet(props.record);
};
</script>

<style scoped>
.meet-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
