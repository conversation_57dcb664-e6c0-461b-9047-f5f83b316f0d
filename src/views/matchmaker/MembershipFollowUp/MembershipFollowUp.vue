<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue';
import {
  Input,
  Table,
  Select,
  Button,
  message,
  Space,
} from 'ant-design-vue';

import {
  config,
  postV3MembershipFollowUpList,
  type PostV3MembershipFollowUpListRequest,
  type PostV3MembershipFollowUpListResponse,
  adminRights,
} from '@qianshou/service';
import AppointmentFirstMeet from './AppointmentFirstMeet.vue';
import AppointmentSecondMeet from './AppointmentSecondMeet.vue';
import RelationshipCell from './RelationshipCell.vue';
import LoveRelationshipCell from './LoveRelationshipCell.vue';
import MarriageRelationshipCell from './MarriageRelationshipCell.vue';
import { ReportMarried, ReportMeeting } from '@qianshou/component';
import FeedbackCell from './FeedbackCell.vue';
import RelationshipActions from './RelationshipActions.vue';
import AppointmentCell from './AppointmentCell.vue';
import AppointmentSecondMeetCell from './AppointmentSecondMeetCell.vue';
import MeetCell from './MeetCell.vue';
import SecondMeetCell from './SecondMeetCell.vue';
import { createMeetActions } from './hooks/useMeetActions';
import { createSecondMeetActions } from './hooks/useSecondMeetActions';
import { createVideoDateActions } from './hooks/useVideoDate';
import { createWechatExchangeActions } from './hooks/useWechatExchange';
import { createMarriageActions } from './hooks/useMarriageActions';
import UserNameCell from './UserNameCell.vue';
import UserIdCell from './UserIdCell.vue';
import ProgressCell from './ProgressCell.vue';
import MakeDate from '@/views/customer/components/action/MakeDate.vue';
import WechatInfoModal from './WechatInfoModal.vue';
import CancelMeetModal from './CancelMeetModal.vue';
import CancelSecondMeetConfirm from './CancelSecondMeetConfirm.vue';
import ReportSecondMeeting from './ReportSecondMeeting.vue';
import MarriageConfirmModal from './MarriageConfirmModal.vue';

import {
  ServeLevelS,
  InputRangeS,
  StaffOrgMutexSelector
} from '@qianshou/component';

import { dayjsMilliseconds, dayjsDiffDay } from '@qianshou/common';
// import { useRouter } from 'vue-router';

import RelationshipForm from '@/views/customer/components/TablesCardComps/RelationshipForm.vue';


// 跟进状态枚举
const followUpStatusOptions = [
  { label: '放弃跟进', value: -1 },
  { label: '跟进中', value: 0 },
];


// const router = useRouter();

const meetFeedBackOptions = computed(() => [{ label: '未反馈', value: 0 }, ...(config?.value?.selectionEnum?.meetingContactStatus ?? [])])

const secondMeetFeedBackOptions = computed(() => [{ label: '未反馈', value: 0 }, ...(config?.value?.selectionEnum?.secondMeetContactStatus ?? [])])

// 权限控制 - 按照图片中的要求：
// 7. 按红娘姓名搜索：服务主管+服务经理+研发+质检（红娘用）+管理员，可见
const canViewStaffSelect = computed(() => {
  return adminRights.isServeManager ||
    adminRights.isDeveloperOrAdmin ||
    adminRights.isQualityControl ||
    adminRights.isMatchmakerManager;
});

// 8. 按组织架构搜索：服务经理+研发+质检（红娘用）+管理员，可见
const canViewOrgSelect = computed(() => {
  return adminRights.isServeManager ||
    adminRights.isDeveloperOrAdmin ||
    adminRights.isQualityControl ||
    adminRights.isMatchmakerManager;
});

// 定义表单状态类型


type TableRecord = Required<Required<PostV3MembershipFollowUpListResponse>['data']>['list'][number];

const defaultQuery = {
  orderBy: undefined,
  orderType: undefined,
  adminID: undefined,
  orgIDs: undefined,
  followUpStatus: [0],
  userID: undefined,
  passiveID: undefined,
  membershipStage: [],
  userFeedbacks: [],
  passiveFeedbacks: [],
  userMeetFeedbacks: [],
  passiveMeetFeedbacks: [],
  progressDurationMin: undefined,
  progressDurationMax: undefined,
  progress: undefined,
}
// 表单状态
const formState = reactive<PostV3MembershipFollowUpListRequest>({ ...defaultQuery });


// 表格数据
const tableData = ref<TableRecord[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 50,
  total: 0,
});

// API响应数据类型
// type Record = Required<Required<postV3MembershipFollowUpListResponse>['data']>['list'][number];

// 获取列表数据
// 定义事件
const emit = defineEmits(['update:total']);

const fetchList = async () => {
  try {
    loading.value = true;

    // 构建请求参数
    const params: PostV3MembershipFollowUpListRequest = {
      page: pagination.current,
      size: pagination.pageSize,
      ...formState,
      progressDurationMin: formState.progressDurationMin && Number(formState.progressDurationMin),
      progressDurationMax: formState.progressDurationMax && Number(formState.progressDurationMax),
    };

    // 处理空数组，将它们设置为 undefined
    for (const key in params) {
      if (params[key] === '' || params[key] === null) {
        params[key] = undefined;
      }
      if (Array.isArray(params[key]) && params[key].length === 0) {
        params[key] = undefined;
      }
    }

    const res = await postV3MembershipFollowUpList(params);
    tableData.value = res.data?.list ?? [];
    pagination.total = res.data?.total || 0;

    // 发送 total 值给父组件
    emit('update:total', res.data?.total || 0);
  } catch (error) {
    message.error('获取列表失败')
  } finally {
    loading.value = false;
  }
};

// 创建一见相关操作的hooks
const meetActions = createMeetActions(fetchList);

// 创建二见相关操作的hooks
const secondMeetActions = createSecondMeetActions(fetchList);

// 创建视频约会相关操作的hooks
const videoDateActions = createVideoDateActions(fetchList);

// 创建微信交换相关操作的hooks
const wechatExchangeActions = createWechatExchangeActions(fetchList);

// 创建结婚相关操作的hooks
const marriageActions = createMarriageActions(fetchList);

// 关系确认表单引用
const relationshipFormRef = ref();


// 确认交往关系
const confirmRelationship = async (record: TableRecord, type: string) => {
  if (!record?.userInfo?.userID || !record?.passiveInfo?.userID) {
    message.error('缺少必要的用户信息');
    return;
  }

  // 打开关系确认弹窗
  relationshipFormRef.value?.handleShow({
    type, // 建立交往关系
    otherUser: {
      id: record.passiveInfo.userID,
      name: record.passiveInfo.nickName,
      realName: record.passiveInfo.name || record.passiveInfo.nickName // 使用name或nickName作为realName
    },
    userId: record.userInfo.userID // 传入当前用户ID
  });
}

// 删除交往关系
const deleteRelationship = async (record: TableRecord, type: string) => {
  if (!record?.userInfo?.userID || !record?.passiveInfo?.userID) {
    message.error('缺少必要的用户信息');
    return;
  }
  relationshipFormRef.value?.handleShow({
    type, // 解除交往关系
    otherUser: {
      id: record.passiveInfo.userID,
      name: record.passiveInfo.nickName,
      realName: record.passiveInfo.name || record.passiveInfo.nickName // 使用name或nickName作为realName
    },
    userId: record.userInfo.userID // 传入当前用户ID
  });
}

// 重置表单
const handleReset = () => {
  Object.assign(formState, { ...defaultQuery });
  pagination.current = 1;
  fetchList();
};

const search = () => {
  pagination.current = 1;
  fetchList();
}

// 表格列配置
const columns = [
  {
    title: '会员ID',
    dataIndex: ['userInfo', 'userID'],
    key: 'userId',
    width: 70,
    fixed: 'left' as const,
  },
  {
    title: '会员姓名',
    dataIndex: ['userInfo', 'name'],
    key: 'userName',
    width: 178,
    fixed: 'left' as const,
  },
  {
    title: '被动方ID',
    dataIndex: ['passiveInfo', 'userID'],
    key: 'passiveId',
    width: 70,
    fixed: 'left' as const,
  },
  {
    title: '被动方姓名',
    dataIndex: ['passiveInfo', 'name'],
    key: 'passiveName',
    width: 178,
    fixed: 'left' as const,
  },
  {
    title: '当前进展',
    dataIndex: 'progress',
    key: 'progress',
    width: 150,
    sorter: true,
  },
  {
    title: '当前进展停留时间',
    dataIndex: 'progressDuration',
    key: 'progressDuration',
    width: 100,
    sorter: true,
    customRender: ({ text }) => `${text}天`,
  },
  {
    title: '推给被动方时间',
    dataIndex: 'recommendToPassiveTime',
    width: 160,
    customRender: ({ text }) => dayjsMilliseconds(text),
  },
  {
    title: '被动方反馈（推荐）',
    key: 'passiveFeedback',
    width: 160
  },
  {
    title: '牵线进展',
    dataIndex: 'passiveContactProgress',
    width: 120,
    customRender: ({ text, record }) => {
      if (text === '牵线中') {
        return h('span', null, [
          h('span', null, text),
          h('span', { style: { color: '#ff4d4f' } }, `（已牵${dayjsDiffDay(record?.passiveContactCreatedTime)}天）`),
        ]);
      } else {
        return text;
      }
    },
  },
  {
    title: '推给会员时间',
    dataIndex: 'recommendToUserTime',
    key: 'recommendToUserTime',
    width: 160,
    sorter: true,
    customRender: ({ text }) => dayjsMilliseconds(text),
  },
  {
    title: '会员反馈（推卡）',
    key: 'userFeedback',
    width: 160
  },
  {
    title: '预约一见',
    dataIndex: 'appointMeetTime',
    key: 'appointMeetTime',
    width: 200,
  },
  {
    title: '一见',
    dataIndex: 'meetTime',
    key: 'meetTime',
    width: 160,
  },
  {
    title: '一见会员反馈',
    dataIndex: 'userMeetFeedback',
    customRender: ({ text }) => {
      const status = meetFeedBackOptions.value.find(item => item.value === text);
      return status ? status.label : '';
    },
    width: 120,
  },
  {
    title: '一见被动方反馈',
    dataIndex: 'passiveMeetFeedback',
    customRender: ({ text }) => {
      const status = meetFeedBackOptions.value.find(item => item.value === text);
      return status ? status.label : '';
    },
    width: 120,
  },
  {
    title: '预约二见',
    dataIndex: 'appointSecondMeetTime',
    key: 'appointSecondMeetTime',
    width: 200,
  },
  {
    title: '二见',
    dataIndex: 'secondMeetTime',
    key: 'secondMeetTime',
    width: 160,
  },
  {
    title: '二见会员反馈',
    dataIndex: 'userSecondMeetFeedback',
    customRender: ({ text }) => {
      const status = secondMeetFeedBackOptions.value?.find(item => item.value === text);
      return status ? status.label : '';
    },
    width: 120,
  },
  {
    title: '二见被动方反馈',
    dataIndex: 'passiveSecondMeetFeedback',
    customRender: ({ text }) => {
      const status = secondMeetFeedBackOptions.value.find(item => item.value === text);
      return status ? status.label : '';
    },
    width: 120,
  },
  {
    title: '交往',
    width: 160,
    customRender: ({ record }: { record: TableRecord }) => {
      return h(RelationshipCell, {
        record,
        onConfirm: confirmRelationship,
        onDelete: deleteRelationship
      });
    },
  },
  {
    title: '恋爱',
    width: 200,
    customRender: ({ record }: { record: TableRecord }) => {
      return h(LoveRelationshipCell, {
        record,
        onConfirm: confirmRelationship,
        onDelete: deleteRelationship
      });
    },
  },
  {
    title: '结婚',
    width: 160,
    key: 'marriage',
  },
  {
    title: '跟进状态',
    dataIndex: 'followUpStatus',
    width: 120,
    customRender: ({ text }) => {
      const status = followUpStatusOptions.find(item => item.value === text);
      return status ? status.label : '-';
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right' as const,
    customRender: ({ record }) => {
      return h(RelationshipActions, {
        record,
        onUpdate: fetchList
      });
    },
  },
].map(x => ({ ...x, align: 'center' as const }));

// 表格变化处理
const handleTableChange = (pag: any, _filters: any, sorter: any) => {
  // 处理分页
  if (pag && typeof pag.current === 'number') {
    pagination.current = pag.current;
  }
  if (pag && typeof pag.pageSize === 'number') {
    pagination.pageSize = pag.pageSize;
  }

  // 处理排序
  if (sorter && sorter.columnKey) {
    formState.orderBy = sorter.columnKey;
    formState.orderType = sorter.order === 'ascend' ? 'asc' : sorter.order === 'descend' ? 'desc' : undefined;
  } else {
    formState.orderBy = undefined;
    formState.orderType = undefined;
  }

  fetchList();
};

// 这些函数已移动到FeedbackEditor.vue组件中


// 预约一见组件引用
const appointmentFirstMeetRef = ref();

// 预约二见组件引用
const appointmentSecondMeetRef = ref();

// 打开预约一见弹窗
const openAppointment = (record: TableRecord) => {
  appointmentFirstMeetRef.value.show(record);
};

// 打开预约二见弹窗
const openAppointmentSecond = (record: TableRecord) => {
  appointmentSecondMeetRef.value.show(record);
};



// 撤销预约一见的逻辑已移到AppointmentCell组件中


onMounted(() => {
  // 组件挂载时获取列表数据
  fetchList();
});
</script>

<template>
  <div class="membership-follow-up">
    <Space style="flex:1;flex-wrap: wrap; margin-bottom: 16px;">
      <Input :value="formState.userID" @update:value="val => formState.userID = val === '' ? undefined : Number(val)"
        size="small" autocomplete="off" placeholder="请输入会员ID" style="width: 150px" />
      <Input :value="formState.passiveID"
        @update:value="val => formState.passiveID = val === '' ? undefined : Number(val)" size="small"
        autocomplete="off" placeholder="请输入被动方ID" style="width: 150px" />
      <ServeLevelS v-model:value="formState.membershipStage" />
      <Select size="small" style="width: 200px" v-model:value="formState.progress" allowClear mode="multiple"
        :maxTagCount="1" :options="config?.selectionEnum.membershipProgressEnum" placeholder="当前进展" />
      <span>当前进展停留时间：</span>
      <InputRangeS v-model:leftModelValue="formState.progressDurationMin"
        v-model:rightModelValue="formState.progressDurationMax" leftPlaceHolder="时间下限" rightPlaceHolder="时间上限"
        style="width: 126px" />
      <Select size="small" v-model:value="formState.followUpStatus" mode="multiple" :options="followUpStatusOptions"
        placeholder="跟进状态" style="width: 200px" :maxTagCount="1" allowClear />
      <StaffOrgMutexSelector v-model:staffId="formState.adminID" v-model:orgIds="formState.orgIDs"
        :showStaffSelect="canViewStaffSelect" :showOrgSelect="canViewOrgSelect"
        :staffSelectProps="{ placeholder: '请选择红娘', style: 'width: 200px', size: 'small' }"
        :orgCascaderProps="{ placeholder: '请选择组织架构', style: 'width: 200px', size: 'small' }" />
      <span>反馈筛选：</span>
      <Select size="small" style="width: 200px" v-model:value="formState.userFeedbacks" allowClear mode="multiple"
        :maxTagCount="1" :options="config?.selectionEnum?.feedback" placeholder="会员推荐反馈" />
      <Select size="small" style="width: 200px" v-model:value="formState.passiveFeedbacks" allowClear mode="multiple"
        :maxTagCount="1" :options="config?.selectionEnum?.feedback" placeholder="被动方推荐反馈" />
      <Select size="small" style="width: 200px" v-model:value="formState.userMeetFeedbacks" allowClear mode="multiple"
        :maxTagCount="1" :options="meetFeedBackOptions" placeholder="会员一见反馈" />
      <Select size="small" style="width: 200px" v-model:value="formState.passiveMeetFeedbacks" allowClear
        mode="multiple" :maxTagCount="1" :options="meetFeedBackOptions" placeholder="被动方一见反馈" />
      <Button type="primary" size="small" @click="search">查询</Button>
      <Button size="small" @click="handleReset">重置</Button>
    </Space>
    <Table :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination" size="small" bordered
      row-key="id" @change="handleTableChange" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ record, column }: { record: TableRecord, column: { key?: string } }">
        <template v-if="column.key === 'passiveFeedback'">
          <FeedbackCell :record="record" type="passive" @update="fetchList" />
        </template>
        <template v-if="column.key === 'userFeedback'">
          <FeedbackCell :record="record" type="user" @update="fetchList" />
        </template>

        <template v-if="column.key === 'appointMeetTime'">
          <AppointmentCell :record="record" @open-appointment="openAppointment" @update="fetchList" />
        </template>

        <template v-if="column.key === 'appointSecondMeetTime'">
          <AppointmentSecondMeetCell :record="record" @open-appointment-second="openAppointmentSecond"
            @update="fetchList" />
        </template>

        <template v-if="column.key === 'userId'">
          <UserIdCell :record="record" type="user" />
        </template>

        <template v-if="column.key === 'userName'">
          <UserNameCell :record="record" type="user" />
        </template>

        <template v-if="column.key === 'passiveId'">
          <UserIdCell :record="record" type="passive" />
        </template>

        <template v-if="column.key === 'passiveName'">
          <UserNameCell :record="record" type="passive" />
        </template>

        <template v-if="column.key === 'progress'">
          <ProgressCell :record="record" />
        </template>

        <template v-if="column.key === 'meetTime'">
          <MeetCell :record="record" />
        </template>

        <template v-if="column.key === 'secondMeetTime'">
          <SecondMeetCell :record="record" />
        </template>

        <template v-if="column.key === 'marriage'">
          <MarriageRelationshipCell :record="record" />
        </template>
      </template>
    </Table>

    <!-- 预约一见组件 -->
    <AppointmentFirstMeet ref="appointmentFirstMeetRef" @update="fetchList" />

    <!-- 预约二见组件 -->
    <AppointmentSecondMeet ref="appointmentSecondMeetRef" @update="fetchList" />

    <!-- 关系确认组件 -->
    <RelationshipForm ref="relationshipFormRef" @update="fetchList" />

    <!-- 确认结婚组件 -->
    <ReportMarried :ref="marriageActions.reportMarriedRef" @update="marriageActions.handleUpdate" />

    <!-- 解除结婚确认弹窗 -->
    <MarriageConfirmModal />

    <!-- 一见组件 -->
    <ReportMeeting :ref="meetActions.reportMeetingRef" @update="meetActions.handleUpdate" />

    <!-- 二见组件 -->
    <ReportSecondMeeting :ref="secondMeetActions.reportSecondMeetingRef" @update="secondMeetActions.handleUpdate" />

    <!-- 视频约会组件 -->
    <MakeDate :ref="videoDateActions.makeDateRef" @update="videoDateActions.handleUpdate" />

    <MakeDate :ref="wechatExchangeActions.makeDateRef" @update="wechatExchangeActions.handleUpdate" />

    <!-- 微信交换信息不完整弹窗 -->
    <WechatInfoModal :ref="wechatExchangeActions.wechatInfoModalRef" :visible="wechatExchangeActions.modalVisible.value"
      :record="wechatExchangeActions.currentRecord.value"
      @update:visible="(val) => wechatExchangeActions.modalVisible.value = val"
      @close="wechatExchangeActions.closeModal" @edit="wechatExchangeActions.closeModal" />

    <!-- 撤销见面确认弹窗 -->
    <CancelMeetModal v-if="meetActions.cancelConfirmVisible" />

    <!-- 撤销二见确认弹窗 -->
    <CancelSecondMeetConfirm v-if="secondMeetActions.cancelConfirmVisible" />
  </div>
</template>

<style scoped>
.user-id {
  color: #1890ff;
  cursor: pointer;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
  align-items: center;
  gap: 8px;
}

:deep(.ant-form-inline .ant-form-item) {
  margin-right: 0;
  margin-bottom: 0;
}

:deep(.ant-form-inline) {
  display: flex;
  flex-direction: column;
  width: 100%;
}

:deep(.ant-form-item-label) {
  min-width: 120px;
  text-align: right;
}

:deep(.ant-select-selection-placeholder) {
  color: #bfbfbf;
}

:deep(.ant-btn-primary) {
  background-color: #1890ff;
}
</style>