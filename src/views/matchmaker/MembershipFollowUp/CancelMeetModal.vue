<template>
  <a-modal
    v-model:visible="meetActions.cancelConfirmVisible"
    title="撤销见面"
    :maskClosable="false"
    :width="400"
  >
    <p>是否撤销本次见面录入？</p>
    <template #footer>
      <a-button @click="meetActions.cancelConfirmVisible = false">取 消</a-button>
      <a-button
        type="primary"
        @click="meetActions.confirmCancelMeet"
        :loading="meetActions.loading"
      >
        确 定
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
/**
 * 撤销见面确认弹窗组件
 * 使用provide/inject模式获取meetActions
 */
import { useMeetActions } from './hooks/useMeetActions';

// 使用一见相关操作的hooks
const meetActions = useMeetActions();
</script>
