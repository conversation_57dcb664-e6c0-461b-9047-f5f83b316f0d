<script setup lang="ts">
/**
 * 当前进展单元格组件
 * 用于展示当前进展和微信交换状态
 */
import { computed } from 'vue';
import { WechatOutlined } from '@ant-design/icons-vue';
import { config } from '@qianshou/service';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord;
}>();

// 当前进展文本
const progressText = computed(() => {
  const progress = props.record.progress;
  if (!progress) return '';

  // 从config中查找对应的枚举值
  const progressEnum = config?.value?.selectionEnum?.membershipProgressEnum || [];
  const progressItem = progressEnum.find(item => item.value === progress);

  // 如果找到对应的枚举项，返回其label，否则返回原始值
  return progressItem ? progressItem.label : progress;
});

// 是否已交换微信
const hasExchangedWechat = computed(() => {
  return props.record.isExchangeWeChat;
});

// 根据进展状态返回对应的样式类
const progressClass = computed(() => {
  const progress = props.record.progress;
  if (!progress) return '';

  // 根据进展状态的数字值返回对应的样式类
  // 使用UI配色表中的颜色
  switch (Number(progress)) {
    case 1: return 'progress-not-recommended'; // 未推给被动方
    case 2: return 'progress-recommended'; // 已推给被动方
    case 3: return 'progress-passive-rejected'; // 被动方推荐反馈
    case 4: return 'progress-passive-accepted'; // 被动方推荐单向
    case 5: return 'progress-member-recommended'; // 推荐给会员
    case 6: return 'progress-member-rejected'; // 会员推荐反馈
    case 7: return 'progress-member-accepted'; // 会员推荐单向
    case 8: return 'progress-mutual-accepted'; // 推荐双向
    case 9: return 'progress-appointment'; // 预约一见
    case 10: return 'progress-met'; // 一见
    case 11: return 'progress-member-met-rejected'; // 会员一见反馈
    case 12: return 'progress-member-met-accepted'; // 会员一见单向
    case 13: return 'progress-met-mutual-accepted'; // 一见双向
    case 14: return 'progress-appointment-second'; // 预约二见
    case 15: return 'progress-met-second'; // 二见
    case 16: return 'progress-member-met-second-rejected'; // 二见会员反馈
    case 17: return 'progress-member-met-second-accepted'; // 二见会员单向
    case 18: return 'progress-met-second-mutual-accepted'; // 二见双向
    case 19: return 'progress-dating'; // 交往
    case 20: return 'progress-love'; // 恋爱
    case 21: return 'progress-married'; // 结婚
    default: return '';
  }
});
</script>

<template>
  <div class="progress-cell">
    <span :class="progressClass">{{ progressText }}</span>
    <WechatOutlined v-if="hasExchangedWechat" style="margin-left: 4px; color: #5AA658;" />
  </div>
</template>

<style scoped>
.progress-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 未推给被动方 */
.progress-not-recommended {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F0F7FF;
  color: #3C6EB8;
}

/* 已推给被动方 */
.progress-recommended {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #E6F2FA;
  color: #8AB8E0;
}

/* 被动方推荐反馈 */
.progress-passive-rejected {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #D9ECF7;
  color: #72AAD8;
}

/* 被动方推荐单向 */
.progress-passive-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #C5E1F0;
  color: #5E9BC9;
}

/* 推荐给会员 */
.progress-member-recommended {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F0F7ED;
  color: #A8D8A0;
}

/* 会员推荐反馈 */
.progress-member-rejected {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #E5F2E3;
  color: #8DC989;
}

/* 会员推荐单向 */
.progress-member-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #D5EBD1;
  color: #72BB6F;
}

/* 推荐双向 */
.progress-mutual-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #C0DFBC;
  color: #5AA658;
}

/* 预约一见 */
.progress-appointment {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FFF0F5;
  color: #F5C3D3;
}

/* 一见 */
.progress-met {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FFEBF1;
  color: #EBAFC4;
}

/* 会员一见反馈 */
.progress-member-met-rejected {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FCE4EE;
  color: #E092AE;
}

/* 会员一见单向 */
.progress-member-met-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F8D7E9;
  color: #D47E9E;
}

/* 一见双向 */
.progress-met-mutual-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F2C9E2;
  color: #C7608E;
}

/* 预约二见 */
.progress-appointment-second {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FFF0F5;
  color: #F5C3D3;
}

/* 二见 */
.progress-met-second {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FFEBF1;
  color: #EBAFC4;
}

/* 二见会员反馈 */
.progress-member-met-second-rejected {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FCE4EE;
  color: #E092AE;
}

/* 二见会员单向 */
.progress-member-met-second-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F8D7E9;
  color: #D47E9E;
}

/* 二见双向 */
.progress-met-second-mutual-accepted {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F2C9E2;
  color: #C7608E;
}

/* 交往 */
.progress-dating {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FEE5D9;
  color: #E89D6D;
}

/* 恋爱 */
.progress-love {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #FDDB8C;
  color: #E08552;
}

/* 结婚 */
.progress-married {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #F3EFFD;
  color: #B8A5D8;
}
</style>
