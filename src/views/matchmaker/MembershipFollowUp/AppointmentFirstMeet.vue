<script setup lang="ts">
/**
 * 预约一见组件
 * 展示规则：多个关系时，展示的顺序为：会员 > 牵线 > 被动方
 * 没有的时候显示空
 * 如果一见时间填写了，展示一见来源和一见方案
 */
import { ref, reactive, computed } from 'vue';
import { Modal, Form, FormItem, DatePicker, Radio, RadioGroup, Textarea, Tag, message, } from 'ant-design-vue';
import { Button, NameMask } from '@qianshou/ui';
import { config, setAchievement } from '@qianshou/service';
import type { FormInstance } from 'ant-design-vue';
import { dayjsMinutes, dayjsDiffDay } from '@qianshou/common';
import dayjs, { Dayjs } from 'dayjs';

const emits = defineEmits(['update']);

// 弹窗可见性
const visible = ref(false);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive({
  dateTime: undefined as Dayjs | undefined,
  source: undefined as string | undefined,
  plan: undefined as string | undefined,
});

// 计算双向满意时间距今多少天
const mutualSatisfactionDays = computed(() => {
  if (!record.value?.mtuallySatisfactoryTime) return '';
  return `${dayjsDiffDay(record.value.mtuallySatisfactoryTime)}天前`;
});

// 计算用户关系类型 - 按优先级：会员 > 牵线 > 被动方
const getUserRelationType = computed(() => {
  const userInfo = record.value?.userInfo;
  if (!userInfo) return null;

  // 检查是否有会员关系（serveAdmin）
  if (userInfo.serveAdmin?.adminID) {
    return {
      text: '会员',
      color: 'error',
      info: userInfo
    };
  }

  // 检查是否有牵线关系（contactAdmin）
  if (userInfo.contactAdmin?.adminID) {
    return {
      text: '牵线',
      color: '#722ed1',
      info: userInfo
    };
  }

  // 检查是否有被动方关系（passiveAdmin）
  if (userInfo.passiveAdmin?.adminID) {
    return {
      text: '被动方',
      color: 'processing',
      info: userInfo
    };
  }

  return null;
});

// 计算被动方关系类型 - 按优先级：会员 > 牵线 > 被动方
const getPassiveRelationType = computed(() => {
  const passiveInfo = record.value?.passiveInfo;
  if (!passiveInfo) return null;

  // 检查是否有会员关系（serveAdmin）
  if (passiveInfo.serveAdmin?.adminID) {
    return {
      text: '会员',
      color: '#f5222d',
      info: passiveInfo
    };
  }

  // 检查是否有牵线关系（contactAdmin）
  if (passiveInfo.contactAdmin?.adminID) {
    return {
      text: '牵线',
      color: '#722ed1',
      info: passiveInfo
    };
  }

  // 检查是否有被动方关系（passiveAdmin）
  if (passiveInfo.passiveAdmin?.adminID) {
    return {
      text: '被动方',
      color: '#1890ff',
      info: passiveInfo
    };
  }


  return null;
});

// 禁用过去的日期
const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().subtract(10, 'days')
};

const record = ref()
// 打开弹窗
const show = (row: any) => {
  // 设置当前记录
  record.value = row

  // 初始化表单
  form.dateTime = dayjs();
  form.source = undefined;
  form.plan = undefined;

  visible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    if (!form.dateTime) {
      message.error('请选择见面时间');
      return;
    }

    // 确定用户ID和被动方ID
    const userId = record.value?.userInfo?.userID;
    const otherUserId = record.value?.passiveInfo?.userID;

    if (!userId || !otherUserId) {
      message.error('缺少必要的用户信息');
      return;
    }

    await setAchievement({
      userId: userId,
      otherUserId: otherUserId,
      serviceType: "setUpMeeting",
      serviceTime: form.dateTime.valueOf(),
      setUpMeeting: {
        source: form.source,
        plan: form.plan,
      },
    });

    message.success('预约一见创建成功');
    visible.value = false;
    emits('update');
  } catch (error) {
    console.error('预约一见失败:', error);
    message.error('预约一见失败');
  }
};

// 暴露方法给父组件
defineExpose({ show });
</script>

<template>
  <Modal v-model:visible="visible" title="预约一见" :maskClosable="false" width="50%" :footer="null" :zIndex="2000">
    <!-- 推卡双向满意时间 -->
    <div v-if="record?.mtuallySatisfactoryTime" class="mutual-satisfaction-time">
      推卡双向满意时间：{{ dayjsMinutes(record.mtuallySatisfactoryTime) }}
      <span class="days-ago">{{ mutualSatisfactionDays }}</span>
    </div>

    <!-- 双方见面态度 -->
    <div class="meeting-attitude">
      <h3>双方见面态度</h3>

      <div class="attitude-table">
        <div class="table-header">
          <div class="header-cell"></div>
          <div class="header-cell">
            被动方
            <Tag v-if="getPassiveRelationType" :color="getPassiveRelationType.color">{{ getPassiveRelationType.text }}
            </Tag>
          </div>
          <div class="header-cell">
            会员
            <Tag v-if="getUserRelationType" :color="getUserRelationType.color">{{ getUserRelationType.text }}</Tag>
          </div>
        </div>

        <div class="table-row">
          <div class="row-label">ID/姓名</div>
          <div class="row-cell">
            {{ record?.passiveInfo?.userID }}
            ({{ record?.passiveInfo?.gender === 'female' ? '女' : '男' }}，
            <NameMask>{{ record?.passiveInfo?.name }}</NameMask>，
            {{ record?.passiveInfo?.nickName }})
          </div>
          <div class="row-cell">
            {{ record?.userInfo?.userID }}
            ({{ record?.userInfo?.gender === 'male' ? '男' : '女' }}，
            <NameMask>{{ record?.userInfo?.name }}</NameMask>，
            {{ record?.userInfo?.nickName }})
          </div>
        </div>

        <div class="table-row">
          <div class="row-label">归属</div>
          <div class="row-cell">
            <template v-if="record?.passiveInfo?.serveAdmin?.name">
              会员红娘：{{ record.passiveInfo.serveAdmin.name }}
            </template>
            <template v-else-if="record?.passiveInfo?.contactAdmin?.name">
              牵线红娘：{{ record.passiveInfo.contactAdmin.name }}
            </template>
            <template v-else-if="record?.passiveInfo?.passiveAdmin?.name">
              被动方红娘：{{ record.passiveInfo.passiveAdmin.name }}
            </template>
            <template v-else>-</template>
          </div>
          <div class="row-cell">
            <template v-if="record?.userInfo?.serveAdmin?.name">
              会员红娘：{{ record.userInfo.serveAdmin.name }}
            </template>
            <template v-else-if="record?.userInfo?.contactAdmin?.name">
              牵线红娘：{{ record.userInfo.contactAdmin.name }}
            </template>
            <template v-else-if="record?.userInfo?.passiveAdmin?.name">
              被动方红娘：{{ record.userInfo.passiveAdmin.name }}
            </template>
            <template v-else>-</template>
          </div>
        </div>

        <div class="table-row">
          <div class="row-label">见面态度</div>
          <div class="row-cell">
            <template v-if="record?.passiveInfo?.meetAttitude">
              {{config?.selectionEnum?.meetAttitudeEnum?.find(x => x.value ===
                record?.passiveInfo?.meetAttitude)?.label || '-'}}
              <span v-if="record?.passiveInfo?.meetAttitude && record?.passiveInfo?.meetInfoScope">
                ({{ record?.passiveInfo?.meetInfoScope }})
              </span>
            </template>
            <template v-else>-</template>
          </div>
          <div class="row-cell">
            <template v-if="record?.userInfo?.meetAttitude">
              {{config?.selectionEnum?.meetAttitudeEnum?.find(x => x.value === record?.userInfo?.meetAttitude)?.label
                || '-'}}
              <span v-if="record?.userInfo?.meetAttitude && record?.userInfo?.meetInfoScope">
                ({{ record?.userInfo?.meetInfoScope }})
              </span>
            </template>
            <template v-else>-</template>
          </div>
        </div>

        <div class="table-row">
          <div class="row-label">见面时间</div>
          <div class="row-cell">
            <template v-if="record?.passiveInfo?.meetTime?.length">
              {{record.passiveInfo.meetTime.map((time: number) => config?.selectionEnum?.meetTimeEnum?.find(x => x.value
                ===
                time)?.label).join('，')}}
              <span v-if="record?.passiveInfo?.meetInfoScope">
                ({{ record?.passiveInfo?.meetInfoScope }})
              </span>
            </template>
            <template v-else>-</template>
          </div>
          <div class="row-cell">
            <template v-if="record?.userInfo?.meetTime?.length">
              {{record.userInfo.meetTime.map((time: number) => config?.selectionEnum?.meetTimeEnum?.find(x => x.value
                ===
                time)?.label).join('，')}}
              <span v-if="record?.userInfo?.meetInfoScope">
                ({{ record?.userInfo?.meetInfoScope }})
              </span>
            </template>
            <template v-else>-</template>
          </div>
        </div>

        <div class="table-row">
          <div class="row-label">见面方式</div>
          <div class="row-cell">
            <template v-if="record?.passiveInfo?.meetMethod?.length">
              {{record.passiveInfo.meetMethod.map((method: number) => config?.selectionEnum?.meetMethodEnum?.find(x =>
                x.value ===
                method)?.label).join('，')}}
              <span v-if="record?.passiveInfo?.meetInfoScope">
                ({{ record?.passiveInfo?.meetInfoScope }})
              </span>
            </template>
            <template v-else>-</template>
          </div>
          <div class="row-cell">
            <template v-if="record?.userInfo?.meetMethod?.length">
              {{record.userInfo.meetMethod.map((method: number) => config?.selectionEnum?.meetMethodEnum?.find(x =>
                x.value
                ===
                method)?.label).join('，')}}
              <span v-if="record?.userInfo?.meetInfoScope">
                ({{ record?.userInfo?.meetInfoScope }})
              </span>
            </template>
            <template v-else>-</template>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约见面表单 -->
    <div class="appointment-form">
      <h3>预约见面</h3>

      <Form ref="formRef" :model="form" autocomplete="off" layout="vertical">
        <FormItem label="一见时间" name="dateTime" :rules="[{ required: true, message: '请选择见面时间' }]">
          <DatePicker v-model:value="form.dateTime" placeholder="请选择约会时间" :showTime="{ format: 'HH:mm' }"
            format="YYYY-MM-DD HH:mm" :disabledDate="disabledDate" style="width: 100%" />
        </FormItem>

        <FormItem label="一见来源" name="source" :rules="[{ required: true, message: '请选择见面来源' }]">
          <RadioGroup v-model:value="form.source" style="width: 100%">
            <Radio value="user">用户自己约见</Radio>
            <Radio value="admin">红娘撮合见面</Radio>
          </RadioGroup>
          <div class="form-hint">（「红娘撮合见面」指红娘帮双方确定约会时间、制定约会计划等）</div>
        </FormItem>

        <FormItem v-if="form.source === 'admin'" label="一见方案" name="plan"
          :rules="[{ required: true, message: '请填写见面方案' }]">
          <Textarea v-model:value="form.plan" :autoSize="{ minRows: 4 }" :maxlength="300" showCount
            placeholder="见面地点，着装建议，约会活动，见面礼物准备等" />
        </FormItem>
      </Form>
    </div>

    <div class="footer">
      <Button @click="visible = false">取 消</Button>
      <Button type="primary" @click="handleSubmit">确 定</Button>
    </div>
  </Modal>
</template>

<style scoped>
.mutual-satisfaction-time {
  margin-bottom: 20px;
  font-size: 14px;
}

.days-ago {
  color: #ff4d4f;
  margin-left: 8px;
}

.meeting-attitude h3,
.appointment-form h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}

.attitude-table {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.header-cell {
  flex: 1;
  padding: 12px;
  text-align: center;
}

.header-cell:first-child {
  flex: 0.5;
  text-align: left;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.row-label {
  flex: 0.5;
  padding: 12px;
  font-weight: 500;
  background-color: #fafafa;
}

.row-cell {
  flex: 1;
  padding: 12px;
}

.form-hint {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.footer {
  margin-top: 24px;
  text-align: right;
}

.footer button {
  margin-left: 8px;
}
</style>
