<template>
  <div class="second-meet-cell">
    <!-- 有权限用户（研发+质检）的显示逻辑 -->
    <template v-if="hasPermission">
      <!-- 有二见时间：显示时间和撤销按钮 -->
      <template v-if="record.secondMeetTime">
        <span>{{ dayjsMinutes(record.secondMeetTime) }}</span>
        <Button type="link" size="small" @click="handleCancelSecondMeet" style="margin-left: 4px" title="撤销">
          <DeleteOutlined style="color: #f50" />
        </Button>
      </template>

      <!-- 有一见时间但没有二见时间：显示确认二见按钮 -->
      <template v-else-if="record.meetTime">
        <Button type="link" size="small" @click.stop="handleOpenConfirmSecondMeet" style="margin-left: 4px">
          确认二见
          <EditOutlined />
        </Button>
      </template>

      <!-- 没有一见时间：显示占位符 -->
      <template v-else>
        -
      </template>
    </template>

    <!-- 无权限用户的显示逻辑 -->
    <template v-else>
      <!-- 有二见时间：只显示时间 -->
      <template v-if="record.secondMeetTime">
        {{ dayjsMinutes(record.secondMeetTime) }}
      </template>

      <!-- 没有二见时间：显示占位符 -->
      <template v-else>
        -
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
/**
 * 二见单元格组件
 * 用于展示二见状态和操作
 *
 * 权限逻辑：
 * 1. 研发+质检（红娘用）权限：
 *    - 有二见时间：显示时间和撤销按钮
 *    - 有一见时间但没有二见时间：显示确认二见按钮和编辑图标
 *    - 没有一见时间：显示占位符"-"
 *
 * 2. 其他权限：
 *    - 有二见时间：只显示时间
 *    - 没有二见时间：显示占位符"-"
 */
import { Button } from 'ant-design-vue';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { adminRights } from '@qianshou/service';
import { dayjsMinutes } from '@qianshou/common';
import type { TableRecord } from './types';
import { computed } from 'vue';
import { useSecondMeetActions } from './hooks/useSecondMeetActions';

const props = defineProps<{
  record: TableRecord;
}>();

// 使用二见相关操作的hooks
const secondMeetActions = useSecondMeetActions();

// 判断是否有权限（研发+质检）
const hasPermission = computed(() => {
  return adminRights.isDeveloperOrAdmin || adminRights.isQualityControl;
});

// 打开确认二见弹窗
const handleOpenConfirmSecondMeet = () => {
  secondMeetActions.openConfirmSecondMeet(props.record);
};

// 打开撤销二见确认弹窗
const handleCancelSecondMeet = () => {
  secondMeetActions.openCancelSecondMeet(props.record);
};
</script>

<style scoped>
.second-meet-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
