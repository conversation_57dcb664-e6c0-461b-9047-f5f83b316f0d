<template>

    <!-- 状态1:确认恋爱+编辑：没有恋爱时间-->
  <div v-if="!record.lastLoveStartTime">
    <a-button type="link" class="confirm-love-button" @click.stop="handleConfirm">
      确认恋爱
      <EditOutlined />
    </a-button>
  </div>


    <!-- 状态2: yyyy-mm-dd+编辑icon：有恋爱时间&处于结婚状态，显示最新确认恋爱的时间。-->
   <div v-if="record.isInMarriage && record.lastLoveStartTime" class="love-relationship-cell">
    <span>
      {{ formattedDate }}
    </span>
    <a-button type="link" size="small" class="action-button" @click.stop="handleConfirm">
      <template #icon>
        <EditOutlined />
      </template>
    </a-button>
  </div>

  <!-- 状态3 yyyy-mm-dd（N天）+解除icon：处于恋爱状态。N天=当前时间-最新的确认恋爱时间 -->
  <div v-else-if="record.isInLove && record.lastLoveStartTime" class="love-relationship-cell">
    <a-tag v-if="record.isUserConfirmLove" :color="record.isUserConfirmLove === '已确认' ? 'success' : record.isUserConfirmLove === '未确认' ? 'warning' : 'error'" style="margin-right:0;">
      {{ record.isUserConfirmLove}}
    </a-tag>

    {{ formattedDate }}
    <span class="days-count">({{ daysSinceStart }}天)</span>

    <div class="action-buttons">
      <a-button type="link" danger size="small" class="action-button" @click.stop="handleDelete">
        <template #icon>
          <DeleteOutlined style="color: #f50" />
        </template>
      </a-button>
    </div>
  </div>

  <!-- 状态4:  yyyy-mm-dd（已结束）+编辑icon：有恋爱时间&未处于恋爱状态&未处于结婚状态-->
  <div v-else-if="!record.isInLove && !record.isInMarriage && record.lastLoveStartTime" class="love-relationship-cell">
    <span>
      {{ formattedDate }}
      <span class="love-status-tag">已解除</span>
    </span>
    <a-button type="link" size="small" class="action-button" @click.stop="handleConfirm">
      <template #icon>
        <EditOutlined />
      </template>
    </a-button>
  </div>




</template>

<script setup lang="ts">
import { computed } from 'vue';
import { dayjsDate, dayjsDiffDay } from '@qianshou/common';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';

// 定义组件的 props
const props = defineProps({
  record: {
    type: Object,
    required: true
  }
});

// 定义组件的事件
const emit = defineEmits(['confirm', 'delete']);

// 计算属性：格式化的恋爱开始日期
const formattedDate = computed(() => {
  if (!props.record.lastLoveStartTime) return '';
  return dayjsDate(props.record.lastLoveStartTime);
});

// 计算属性：计算恋爱开始至今的天数
const daysSinceStart = computed(() => {
  if (!props.record.lastLoveStartTime) return 0;
  return dayjsDiffDay(props.record.lastLoveStartTime);
});

// 处理确认恋爱关系
const handleConfirm = (e: MouseEvent) => {
  e.stopPropagation();
  emit('confirm', props.record, 'schedule-courtship');
};

// 处理删除恋爱关系
const handleDelete = (e: MouseEvent) => {
  e.stopPropagation();
  emit('delete', props.record, 'relieve-courtship');
};
</script>

<style scoped>
.love-relationship-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.days-count {
  color: #ff4d4f;
}

.love-status-tag {
  color: #ff4d4f;
  margin-left: 4px;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.action-button {
  padding: 0 4px;
  height: auto;
}

.confirm-love-button {
  padding: 0;
  height: auto;
  color: #1890ff;
}
</style>
