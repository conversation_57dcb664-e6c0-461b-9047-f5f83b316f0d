<script setup lang="ts">
/**
 * 关系操作组件
 * 包含契合分析、跟进状态切换、视频约会、微信录入等功能
 */
import { Dropdown, Menu, Button } from 'ant-design-vue';
import { MoreOutlined } from '@ant-design/icons-vue';
import type { TableRecord } from './types';
import AnalysisAction from './AnalysisAction.vue';
import VideoDateAction from './VideoDateAction.vue';
import WechatAction from './WechatAction.vue';
import FollowUpStatusButton from './FollowUpStatusButton.vue';
import { AIMatchAnalysisDrawer, useMatchAnalysis } from '@qianshou/component';

const props = defineProps<{
  record: TableRecord;
}>();

useMatchAnalysis(props.record.userInfo?.userID)

const emit = defineEmits(['update']);
</script>

<template>
  <div class="relationship-actions">
    <AnalysisAction :record="record" />
    <FollowUpStatusButton :record="record" @update="emit('update')" />

    <Dropdown>
      <Button type="link">
        <MoreOutlined />
      </Button>
      <template #overlay>
        <Menu>
          <VideoDateAction :record="record" />
          <WechatAction :record="record" @update="emit('update')" />
        </Menu>
      </template>
    </Dropdown>
  </div>
  <AIMatchAnalysisDrawer />
</template>

<style scoped>
.relationship-actions {
  display: flex;
  align-items: center;
}

.relationship-actions button {
  padding: 0 8px;
}
</style>
