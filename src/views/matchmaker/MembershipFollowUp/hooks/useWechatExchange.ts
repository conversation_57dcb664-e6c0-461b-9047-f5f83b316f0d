/**
 * 微信交换相关操作的hooks
 * 用于处理微信交换的逻辑
 */
import { ref, provide, inject, InjectionKey } from 'vue';
import { message } from 'ant-design-vue';
import { setAchievement } from '@qianshou/service';
import { ElMessageBox } from 'element-plus';
import type { TableRecord } from '../types';
import MakeDate from '@/views/customer/components/action/MakeDate.vue';
import WechatInfoModal from '../WechatInfoModal.vue';

// 定义注入键
export const wechatExchangeKey: InjectionKey<ReturnType<typeof createWechatExchangeActions>> = Symbol('wechatExchange');

/**
 * 创建微信交换相关操作
 * @param refreshList 刷新列表的回调函数
 * @returns 微信交换相关操作
 */
export function createWechatExchangeActions(refreshList: () => void) {
  // MakeDate组件引用
  const makeDateRef = ref<InstanceType<typeof MakeDate>>();

  // WechatInfoModal组件引用
  const wechatInfoModalRef = ref<InstanceType<typeof WechatInfoModal>>();

  // 信息不完整弹窗状态
  const modalVisible = ref<boolean>(false);
  const currentRecord = ref<TableRecord | null>(null);

  // 不再需要使用useUserEnoughInfo，直接使用record中的字段

  // 打开微信交换弹窗
  const openWechatExchange = (record: TableRecord) => {
    if (!record?.userInfo?.userID || !record?.passiveInfo?.userID) {
      return;
    }

    currentRecord.value = record;

    // 检查信息是否完整
    const userCanExchange = record.userInfo?.canExchangeWechat || false;
    const passiveCanExchange = record.passiveInfo?.canExchangeWechat || false;

    if (!userCanExchange || !passiveCanExchange) {
      modalVisible.value = true;
      return;
    }

    // 调用MakeDate组件的handleDate方法
    makeDateRef.value?.handleDate(
      record.userInfo.userID,
      record.passiveInfo.userID,
      '4', // 微信交换来源
      'wechat' // 约会类型
    );
  };

  // 撤销微信交换
  const revokeWechatExchange = async (record: TableRecord) => {
    if (!record?.userInfo?.userID || !record?.passiveInfo?.userID) {
      return;
    }

    try {
      await ElMessageBox.confirm(
        '是否撤销本次微信录入',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );
      await setAchievement({
        userId: record.userInfo.userID,
        otherUserId: record.passiveInfo.userID,
        serviceType: "revoke-wechat",
      });

      message.success('撤销微信交换成功');
      refreshList();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('撤销微信交换失败:', error);
        message.error('撤销微信交换失败');
      }
    }
  };

  // 处理更新
  const handleUpdate = () => {
    refreshList();
  };

  // 关闭信息不完整弹窗
  const closeModal = () => {
    modalVisible.value = false;
  };

  // 提供上下文
  provide(wechatExchangeKey, {
    makeDateRef,
    wechatInfoModalRef,
    modalVisible,
    currentRecord,
    openWechatExchange,
    revokeWechatExchange,
    handleUpdate,
    closeModal
  });

  return {
    makeDateRef,
    wechatInfoModalRef,
    modalVisible,
    currentRecord,
    openWechatExchange,
    revokeWechatExchange,
    handleUpdate,
    closeModal
  };
}

/**
 * 使用微信交换相关操作
 * @returns 微信交换相关操作
 */
export function useWechatExchange() {
  const context = inject(wechatExchangeKey);

  if (!context) {
    throw new Error('useWechatExchange must be used within a provider');
  }

  return context;
}
