import { ref, provide, inject, InjectionKey } from 'vue';
import { message } from 'ant-design-vue';
import { setAchievement } from '@qianshou/service';
import dayjs from 'dayjs';
import type { TableRecord } from '../types';
import { ReportMeeting } from '@qianshou/component';

/**
 * 一见相关操作的hooks接口
 */
export interface MeetActionsContext {
  // 状态
  cancelConfirmVisible: boolean;
  currentRecord: TableRecord | null;
  loading: boolean;
  reportMeetingRef: any;

  // 方法
  openCancelMeet: (record: TableRecord) => void;
  confirmCancelMeet: () => Promise<boolean | undefined>;
  openConfirmMeet: (record: TableRecord) => void;
  handleUpdate: () => void;
}

// 创建注入键
export const MeetActionsKey: InjectionKey<MeetActionsContext> = Symbol('MeetActions');

/**
 * 创建一见相关操作的hooks
 * 封装确认见面和撤销见面的逻辑
 * @param updateCallback 数据更新后的回调函数
 */
export function createMeetActions(updateCallback: () => void) {
  // 撤销一见确认弹窗可见性
  const cancelConfirmVisible = ref(false);
  // 当前操作的记录
  const currentRecord = ref<TableRecord | null>(null);
  // 加载状态
  const loading = ref(false);
  // ReportMeeting 组件引用
  const reportMeetingRef = ref<InstanceType<typeof ReportMeeting> | null>(null);

  // 打开撤销一见确认弹窗
  const openCancelMeet = (record: TableRecord) => {
    currentRecord.value = record;
    cancelConfirmVisible.value = true;
  };

  // 确认撤销一见
  const confirmCancelMeet = async () => {
    if (!currentRecord.value) {
      message.error('缺少必要的用户信息');
      return;
    }

    try {
      loading.value = true;

      // 确定用户ID和被动方ID
      const userId = currentRecord.value.userInfo?.userID;
      const otherUserId = currentRecord.value.passiveInfo?.userID;

      if (!userId || !otherUserId) {
        message.error('缺少必要的用户信息');
        return;
      }

      await setAchievement({
        userId: userId,
        otherUserId: otherUserId,
        serviceType: "revoke-meet",
        serviceTime: dayjs().valueOf(),
      });

      message.success('撤销见面成功');
      cancelConfirmVisible.value = false;
      currentRecord.value = null;
      updateCallback();
      return true;
    } catch (error) {
      console.error('撤销见面失败:', error);
      message.error('撤销见面失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 打开确认一见弹窗
  const openConfirmMeet = (record: TableRecord) => {
    const { gender, nickName, name, userID } = record.passiveInfo || {};
    const { gender: userGender, nickName: userNickName, name: userName, userID: userUserID } = record.userInfo || {};

    reportMeetingRef.value?.show(
      {
        gender: gender === 'male' ? 1 : gender === 'female' ? 2 : 0,
        name: nickName,
        id: userID,
        realName: name
      },
      {
        gender: userGender === 'male' ? 1 : userGender === 'female' ? 2 : 0,
        name: userNickName,
        id: userUserID,
        realName: userName
      }
    );
  };

  // 数据更新处理函数
  const handleUpdate = () => {
    updateCallback();
  };

  // 创建上下文对象

  const context: MeetActionsContext = {
    get cancelConfirmVisible() { return cancelConfirmVisible.value; },
    set cancelConfirmVisible(val) { cancelConfirmVisible.value = val; },
    get currentRecord() { return currentRecord.value; },
    get loading() { return loading.value; },
    reportMeetingRef,
    openCancelMeet,
    confirmCancelMeet,
    openConfirmMeet,
    handleUpdate
  };

  // 提供上下文
  provide(MeetActionsKey, context);

  return context;
}

/**
 * 使用一见相关操作的hooks
 * 在子组件中通过inject获取
 */
export function useMeetActions(): MeetActionsContext {
  const context = inject(MeetActionsKey);
  if (!context) {
    throw new Error('useMeetActions must be used within a component that has createMeetActions provided');
  }
  return context;
}
