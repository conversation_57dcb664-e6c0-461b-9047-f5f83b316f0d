/**
 * 结婚相关操作的hooks
 * 用于处理结婚关系的确认和解除
 */
import { ref, provide, inject, InjectionKey } from 'vue';
import { message } from 'ant-design-vue';
import { setAchievement } from '@qianshou/service';
import type { TableRecord } from '../types';
import { ReportMarried } from '@qianshou/component';

/**
 * 结婚相关操作的hooks接口
 */
export interface MarriageActionsContext {
  // 状态
  confirmDialogVisible: boolean;
  currentRecord: TableRecord | null;
  loading: boolean;
  reportMarriedRef: any;

  // 方法
  showCreateMarried: (record: TableRecord) => void;
  handleDelete: (record: TableRecord) => void;
  confirmDelete: () => Promise<boolean>;
  handleUpdate: () => void;
}

// 创建注入键
export const MarriageActionsKey: InjectionKey<MarriageActionsContext> = Symbol('MarriageActions');

/**
 * 创建结婚相关操作的hooks
 * 封装确认结婚和解除结婚的逻辑
 * @param updateCallback 数据更新后的回调函数
 */
export function createMarriageActions(updateCallback: () => void) {
  // 解除结婚确认弹窗可见性
  const confirmDialogVisible = ref(false);
  // 当前操作的记录
  const currentRecord = ref<TableRecord | null>(null);
  // 加载状态
  const loading = ref(false);
  // ReportMarried 组件引用
  const reportMarriedRef = ref<InstanceType<typeof ReportMarried> | null>(null);

  // 打开确认结婚弹窗
  const showCreateMarried = (record: TableRecord) => {
    const { gender, nickName, userID } = record.passiveInfo || {};
    const { gender: userGender, nickName: userNickName, userID: userUserID } = record.userInfo || {};
    
    reportMarriedRef.value?.show({
      otherUserInfo: {
        gender: gender === 'male' ? 1 : gender === 'female' ? 2 : 0,
        name: nickName,
        id: userID
      },
      userInfo: {
        gender: userGender === 'male' ? 1 : userGender === 'female' ? 2 : 0,
        name: userNickName,
        id: userUserID
      }
    });
  };

  // 处理删除结婚关系
  const handleDelete = (record: TableRecord) => {
    currentRecord.value = record;
    confirmDialogVisible.value = true;
  };

  // 确认删除结婚关系
  const confirmDelete = async () => {
    if (!currentRecord.value) {
      message.error('缺少必要的用户信息');
      return false;
    }

    try {
      loading.value = true;

      await setAchievement({
        userId: currentRecord.value.userInfo?.userID,
        otherUserId: currentRecord.value.passiveInfo?.userID,
        serviceType: "revoke-married"
      });
      
      message.success('解除结婚关系成功');
      confirmDialogVisible.value = false;
      currentRecord.value = null;
      updateCallback();
      return true;
    } catch (error) {
      message.error('解除结婚关系失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 数据更新处理函数
  const handleUpdate = () => {
    updateCallback();
  };

  // 创建上下文对象
  const context: MarriageActionsContext = {
    get confirmDialogVisible() { return confirmDialogVisible.value; },
    set confirmDialogVisible(val) { confirmDialogVisible.value = val; },
    get currentRecord() { return currentRecord.value; },
    get loading() { return loading.value; },
    reportMarriedRef,
    showCreateMarried,
    handleDelete,
    confirmDelete,
    handleUpdate
  };

  // 提供上下文
  provide(MarriageActionsKey, context);

  return context;
}

/**
 * 使用结婚相关操作的hooks
 * 在子组件中通过inject获取
 */
export function useMarriageActions(): MarriageActionsContext {
  const context = inject(MarriageActionsKey);
  if (!context) {
    throw new Error('useMarriageActions must be used within a component that has createMarriageActions provided');
  }
  return context;
}
