import { ref, provide, inject, InjectionKey } from 'vue';
import { message } from 'ant-design-vue';
import { setAchievement } from '@qianshou/service';
import dayjs from 'dayjs';
import type { TableRecord } from '../types';

/**
 * 二见相关操作的hooks接口
 */
export interface SecondMeetActionsContext {
  // 状态
  cancelConfirmVisible: boolean;
  currentRecord: TableRecord | null;
  loading: boolean;
  reportSecondMeetingRef: any;

  // 方法
  openCancelSecondMeet: (record: TableRecord) => void;
  confirmCancelSecondMeet: () => Promise<boolean | undefined>;
  openConfirmSecondMeet: (record: TableRecord) => void;
  handleUpdate: () => void;
}

// 创建注入键
export const SecondMeetActionsKey: InjectionKey<SecondMeetActionsContext> = Symbol('SecondMeetActions');

/**
 * 创建二见相关操作的hooks
 * 封装确认二见和撤销二见的逻辑
 * @param updateCallback 数据更新后的回调函数
 */
export function createSecondMeetActions(updateCallback: () => void) {
  // 撤销二见确认弹窗可见性
  const cancelConfirmVisible = ref(false);
  // 当前操作的记录
  const currentRecord = ref<TableRecord | null>(null);
  // 加载状态
  const loading = ref(false);
  // ReportSecondMeeting 组件引用
  const reportSecondMeetingRef = ref<any>(null);

  // 打开撤销二见确认弹窗
  const openCancelSecondMeet = (record: TableRecord) => {
    currentRecord.value = record;
    cancelConfirmVisible.value = true;
  };

  // 确认撤销二见
  const confirmCancelSecondMeet = async (): Promise<boolean | undefined> => {
    if (!currentRecord.value) return;

    const record = currentRecord.value;
    const userId = record.userInfo?.userID;
    const otherUserId = record.passiveInfo?.userID;

    if (!userId || !otherUserId) {
      message.error('缺少必要的用户信息');
      return false;
    }

    try {
      loading.value = true;

      await setAchievement({
        userId: userId,
        otherUserId: otherUserId,
        serviceType: "revoke-meet-second",
        serviceTime: dayjs().valueOf(),
      });

      message.success('撤销二见成功');
      cancelConfirmVisible.value = false;
      currentRecord.value = null;
      updateCallback();
      return true;
    } catch (error) {
      console.error('撤销二见失败:', error);
      message.error('撤销二见失败');
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 打开确认二见弹窗
  const openConfirmSecondMeet = (record: TableRecord) => {
    const { gender, nickName, name, userID } = record.passiveInfo || {};
    const { gender: userGender, nickName: userNickName, name: userName, userID: userUserID } = record.userInfo || {};

    reportSecondMeetingRef.value?.show(
      {
        gender: gender === 'male' ? 1 : gender === 'female' ? 2 : 0,
        name: nickName,
        id: userID,
        realName: name
      },
      {
        gender: userGender === 'male' ? 1 : userGender === 'female' ? 2 : 0,
        name: userNickName,
        id: userUserID,
        realName: userName
      }
    );
  };

  // 数据更新处理函数
  const handleUpdate = () => {
    updateCallback();
  };

  // 创建上下文对象
  const context: SecondMeetActionsContext = {
    get cancelConfirmVisible() { return cancelConfirmVisible.value; },
    set cancelConfirmVisible(val) { cancelConfirmVisible.value = val; },
    get currentRecord() { return currentRecord.value; },
    get loading() { return loading.value; },
    reportSecondMeetingRef,
    openCancelSecondMeet,
    confirmCancelSecondMeet,
    openConfirmSecondMeet,
    handleUpdate
  };

  // 提供上下文
  provide(SecondMeetActionsKey, context);

  return context;
}

/**
 * 使用二见相关操作的hooks
 * 在子组件中通过inject获取
 */
export function useSecondMeetActions(): SecondMeetActionsContext {
  const context = inject(SecondMeetActionsKey);
  if (!context) {
    throw new Error('useSecondMeetActions must be used within a component that has createSecondMeetActions provided');
  }
  return context;
}
