/**
 * 视频约会相关操作的hooks
 * 用于处理视频约会的逻辑
 */
import { ref, provide, inject, InjectionKey } from 'vue';
import type { TableRecord } from '../types';
import MakeDate from '@/views/customer/components/action/MakeDate.vue';

// 定义注入键
export const videoDateKey: InjectionKey<ReturnType<typeof createVideoDateActions>> = Symbol('videoDate');

/**
 * 创建视频约会相关操作
 * @param refreshList 刷新列表的回调函数
 * @returns 视频约会相关操作
 */
export function createVideoDateActions(refreshList: () => void) {
  // MakeDate组件引用
  const makeDateRef = ref<InstanceType<typeof MakeDate>>();

  // 打开视频约会弹窗
  const openVideoDate = (record: TableRecord) => {
    if (!record?.userInfo?.userID || !record?.passiveInfo?.userID) {
      return;
    }

    // 调用MakeDate组件的handleDate方法
    makeDateRef.value?.handleDate(
      record.userInfo.userID,
      record.passiveInfo.userID,
      '4', // 视频约会来源
      'dating' // 约会类型
    );
  };

  // 处理更新
  const handleUpdate = () => {
    refreshList();
  };

  // 提供上下文
  provide(videoDateKey, {
    makeDateRef,
    openVideoDate,
    handleUpdate
  });

  return {
    makeDateRef,
    openVideoDate,
    handleUpdate
  };
}

/**
 * 使用视频约会相关操作
 * @returns 视频约会相关操作
 */
export function useVideoDate() {
  const context = inject(videoDateKey);

  if (!context) {
    throw new Error('useVideoDate must be used within a provider');
  }

  return context;
}
