<template>
  <div class="marriage-relationship-cell">

    <!-- 状态1 确认结婚+编辑icon：没有结婚时间-->
    <div v-if="!record.lastMarriageStartTime" class="confirm-marriage-container">
      <Button type="link" class="confirm-marriage-button" @click.stop="handleConfirmMarriage">
        确认结婚
        <EditOutlined />
      </Button>
    </div>

    <!-- 状态2: yyyy-mm-dd+解除icon：处于结婚关系（即确认了结婚关系，且未取消）就显示确认结婚关系的日期。-->
    <div v-if="record.isInMarriage && record.lastMarriageStartTime" class="marriage-status">
      <span>
        {{ formattedDate }}
      </span>
      <Button type="link" danger size="small" class="action-button" @click.stop="handleDeleteMarriage" title="解除结婚">
        <DeleteOutlined />
      </Button>
    </div>

    <!-- 状态3 yyyy-mm-dd（已取消）+解除icon：有结婚时间&未处于结婚状态-->
    <div v-else-if="!record.isInMarriage && record.lastMarriageStartTime" class="marriage-status">
      <span>
        {{ formattedDate }}
        <span class="marriage-status-tag">已解除</span>
      </span>
      <Button type="link" size="small" class="action-button" @click.stop="handleConfirmMarriage" title="确认结婚">
        <EditOutlined />
      </Button>
    </div>

  </div>
</template>

<script setup lang="ts">
/**
 * 结婚关系单元格组件
 * 用于展示结婚状态和操作
 *
 * 状态逻辑：
 * 1. isInMarriage=true, 有lastMarriageStartTime - 显示结婚时间+删除按钮
 * 2. isInMarriage=false, 有lastMarriageStartTime - 显示时间+已解除标签+编辑按钮
 * 3. isInMarriage=false, 无lastMarriageStartTime - 显示确认结婚按钮
 * 4. 其他情况 - 显示占位符"-"
 */
import { computed } from 'vue';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { Button } from 'ant-design-vue';
import { dayjsDate } from '@qianshou/common';
import type { TableRecord } from './types';
import { useMarriageActions } from './hooks/useMarriageActions';

// 定义组件的 props
const props = defineProps<{
  record: TableRecord;
}>();

// 使用结婚相关操作的hooks
const marriageActions = useMarriageActions();

// 计算属性：格式化的结婚开始日期
const formattedDate = computed(() => {
  if (!props.record.lastMarriageStartTime) return '';
  return dayjsDate(props.record.lastMarriageStartTime);
});

// 处理确认结婚关系
const handleConfirmMarriage = () => {
  marriageActions.showCreateMarried(props.record);
};

// 处理删除结婚关系
const handleDeleteMarriage = () => {
  marriageActions.handleDelete(props.record);
};
</script>

<style scoped>
.marriage-relationship-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.marriage-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.marriage-status-tag {
  color: #ff4d4f;
  margin-left: 4px;
}

.confirm-marriage-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-marriage-button {
  padding: 0;
  height: auto;
  color: #1890ff;
}

.action-button {
  padding: 0 4px;
  height: auto;
}
</style>
