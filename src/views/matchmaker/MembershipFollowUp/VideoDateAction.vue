<script setup lang="ts">
/**
 * 视频约会按钮组件
 * 用于显示视频约会按钮并处理点击事件
 */
import { VideoCameraOutlined } from '@ant-design/icons-vue';
import { Menu, Button } from 'ant-design-vue';
import { computed } from 'vue';
import type { TableRecord } from './types';
import { useVideoDate } from './hooks/useVideoDate';

const props = defineProps<{
  record: TableRecord;
}>();

// 使用视频约会hooks
const { openVideoDate } = useVideoDate();

// 判断是否已经视频约会
const hasVideoDate = computed(() => {
  return props.record.achievement?.includes('schedule-dating') || false;
});

// 视频约会
const arrangeVideoDate = () => {
  if (hasVideoDate.value) return;
  openVideoDate(props.record);
};
</script>

<template>
  <Menu.Item key="video" @click="arrangeVideoDate" v-if="!hasVideoDate">
    <VideoCameraOutlined />
    视频约会
  </Menu.Item>
  <Menu.Item key="video-disabled" v-else>
    <Button type="link" disabled>
      <VideoCameraOutlined />
      已视频约会
    </Button>
  </Menu.Item>
</template>
