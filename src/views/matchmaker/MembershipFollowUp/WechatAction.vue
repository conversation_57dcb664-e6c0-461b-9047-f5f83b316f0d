<script setup lang="ts">
/**
 * 微信操作按钮组件
 * 用于显示微信录入/撤销按钮并处理点击事件
 */
import { WechatOutlined } from '@ant-design/icons-vue';
import { Menu, Button } from 'ant-design-vue';
import { computed } from 'vue';
import type { TableRecord } from './types';
import { useWechatExchange } from './hooks/useWechatExchange';

const props = defineProps<{
  record: TableRecord;
}>();

// 使用微信交换hooks
const { openWechatExchange, revokeWechatExchange } = useWechatExchange();

// 判断是否已经交换微信
const hasWechatExchange = computed(() => {
  return props.record.achievement?.includes('schedule-wechat') || false;
});

// 处理微信操作
const handleWechatAction = () => {
  if (hasWechatExchange.value) {
    // 撤销微信交换
    revokeWechatExchange(props.record);
  } else {
    // 录入微信交换
    openWechatExchange(props.record);
  }
};
</script>

<template>
  <Menu.Item key="revoke-wechat" v-if="hasWechatExchange" @click="handleWechatAction">
    <Button type="link" color="#faad14">
      <WechatOutlined />
      撤销微信交换录入
    </Button>
  </Menu.Item>
  <Menu.Item key="wechat" v-else @click="handleWechatAction">
    <Button type="link" color="#faad14">
      <WechatOutlined />
      微信
    </Button>
  </Menu.Item>
</template>
