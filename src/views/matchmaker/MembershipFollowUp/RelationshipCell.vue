<template>
  <div>

     <!-- 1. 确认交往+编辑：没有交往时间 -->
    <div v-if="!record.lastRelationshipStartTime">
      <a-button type="link" class="confirm-love-button" @click.stop="handleConfirm">
        确认交往
        <EditOutlined />
      </a-button>
    </div>

    <!-- 3 .yyyy-mm-dd（N天）：处于交往状态。显示当时确认交往的时间，N天=当前时间-最新的确认交往时间 -->
    <div v-if="record.isInRelationship &&  record.lastRelationshipStartTime" class="relationship-cell">
      <span>
        {{ formattedDate }}
        <span class="days-count">({{ daysSinceStart }}天)</span>
      </span>
      <!-- 根据需求，这里不显示编辑按钮 -->
      <a-button type="link" danger size="small" class="action-button" @click.stop="handleDelete">
        <template #icon>
          <DeleteOutlined />
        </template>
      </a-button>
    </div>

    <!-- 2和4. yyyy-mm-dd+编辑：有交往时间&未处于交往状态&处于恋爱状态或者处于结婚状态 
     yyyy-mm-dd（已结束）：有交往时间&未处于交往状态&没有处于恋爱或者结婚状态
      -->
    <div v-else-if="!record.isInRelationship && record.lastRelationshipStartTime" class="relationship-cell">
      <!-- 只有在没有婚姻/恋爱关系时才显示"已解除"标签 -->
      <span v-if="!record.isInMarriage && !record.isInLove"  class="love-status-tag">已解除</span>
      <span>{{ formattedDate }}</span>
      <a-button type="link" size="small" class="action-button" @click.stop="handleConfirm">
        <template #icon>
          <EditOutlined />
        </template>
      </a-button>
    </div>

   
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { dayjsDate, dayjsDiffDay } from '@qianshou/common';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';

import type { TableRecord } from './types';

// 定义组件的 props
const props = defineProps<{
  record: TableRecord;
}>();

// 定义组件的事件
const emit = defineEmits(['confirm', 'delete']);

// 计算属性：格式化的交往开始日期
const formattedDate = computed(() => {
  if (!props.record.lastRelationshipStartTime) return '';
  return dayjsDate(props.record.lastRelationshipStartTime);
});

// 计算属性：计算交往开始至今的天数
const daysSinceStart = computed(() => {
  if (!props.record.lastRelationshipStartTime) return 0;
  return dayjsDiffDay(props.record.lastRelationshipStartTime);
});

// 处理确认交往关系
const handleConfirm = (e: MouseEvent) => {
  e.stopPropagation();
  emit('confirm', props.record, 'schedule-intercourse');
};

// 处理删除交往关系
const handleDelete = (e: MouseEvent) => {
  e.stopPropagation();
  emit('delete', props.record, 'relieve-intercourse');
};
</script>

<style scoped>
.relationship-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.days-count {
  color: #ff4d4f;
}

.action-button {
  padding: 0 4px;
  height: auto;
}

.confirm-love-button {
  padding: 0;
  height: auto;
  color: #1890ff;
}

.love-status-tag {
  color: #ff4d4f;
  margin-left: 4px;
}
</style>

