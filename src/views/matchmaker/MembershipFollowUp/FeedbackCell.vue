<script setup lang="ts">
/**
 * 反馈单元格组件
 * 用于展示和编辑会员或被动方的推卡反馈
 */
import { computed, ref } from 'vue';
import { Select, Badge, message } from 'ant-design-vue';
import { feedbackColor } from '@qianshou/common';
import { config, request, } from '@qianshou/service';
import type { TableRecord } from './types';
import FeedbackRejectModal from '@/views/customer/components/TablesCardComps/FeedbackRejectModal.vue';

const props = defineProps<{
  record: TableRecord;
  type: 'user' | 'passive'; // 用户类型：会员或被动方
}>();

// 当前反馈值
const currentFeedback = computed(() => {
  return props.type === 'user' ? props.record.userFeedback : props.record.passiveFeedback;
});

const emit = defineEmits(['update']);

/** 修改精选列表反馈状态 */
const selectionFeedbackChange = async (record: TableRecord, changeType: {
  /**
   * 会员反馈
   */
  feedback?: string
  /**
   * 会员深沟后反馈
   */
  secondFeedback?: string
  /**
   * 被动方反馈
   */
  passiveFeedback?: string
  /**
   * 被动方深沟后反馈
   */
  passiveSecondFeedback?: string
}) => {
  try {
    await request({
      url: "/v3/selection",
      method: "PUT",
      data: {
        ...changeType,
        userId: record.userInfo?.userID,
        otherUserId: record.passiveInfo?.userID,
      },
    });
  } catch { }
}

// 拒绝反馈弹窗
const rejectModalVisible = ref(false);
const rejectTitle = ref('');

// 修改反馈
const changeFeedback = async (val: string, record: TableRecord) => {
  try {
    // 如果是拒绝接触，显示拒绝原因弹窗
    if (val === 'contactRejected') {
      if (!record.userInfo?.userID || !record.passiveInfo?.userID) {
        message.error('缺少必要的用户信息');
        return false;
      }

      showRejectModal(record);
      return false;
    }

    // 使用selectionFeedbackChange函数更新反馈
    if (props.type === 'user') {
      await selectionFeedbackChange(record, { feedback: val });
    } else {
      await selectionFeedbackChange(record, { passiveFeedback: val });
    }

    const userType = props.type === 'user' ? '会员' : '被动方';
    message.success(`${userType}反馈更新成功`);
    return true;
  } catch (error) {
    console.error('更新反馈失败:', error);
    message.error('更新反馈失败，请重试');
    return false;
  }
};

// 显示拒绝原因弹窗
const showRejectModal = (record: TableRecord) => {
  const userType = props.type === 'user' ? '会员' : '被动方';
  const otherType = props.type === 'user' ? '被动方' : '会员';

  const userName = props.type === 'user'
    ? `${record.userInfo?.name?.charAt(0)},${record.userInfo?.gender === 'female' ? '女' : '男'},${record.userInfo?.userID}`
    : `${record.passiveInfo?.name?.charAt(0)},${record.passiveInfo?.gender === 'female' ? '女' : '男'},${record.passiveInfo?.userID}`;

  rejectTitle.value = `${userType}（${userName}）拒绝接触${otherType}的原因是？`;


  rejectModalVisible.value = true;
};

// 更新反馈
const updateFeedback = async (value: any) => {
  const strValue = String(value);
  const result = await changeFeedback(strValue, props.record);

  if (result) {
    emit('update');
  }
};

// 拒绝原因提交
const handleRejectSubmit = async () => {
  // 使用selectionFeedbackChange函数更新拒绝反馈
  if (props.type === 'user') {
    await selectionFeedbackChange(props.record, { feedback: 'contactRejected' });
  } else {
    await selectionFeedbackChange(props.record, { passiveFeedback: 'contactRejected' });
  }

  rejectModalVisible.value = false;
  emit('update');
};
</script>

<template>
  <div>
    <Select style="width: 100%" :value="currentFeedback" size="small" :bordered="false" @select="updateFeedback">
      <Select.Option v-for="(item, ind) in config?.selectionEnum?.feedback" :key="item.value" :label="item.label"
        :value="item.value">
        <Badge :color="feedbackColor[ind % 8]" :text="item.label" />
      </Select.Option>
    </Select>

    <!-- 拒绝原因弹窗 -->
    <FeedbackRejectModal v-if="rejectModalVisible" v-model:visible="rejectModalVisible" :title="rejectTitle"
      :userId="record.userInfo?.userID" :otherUserId="record.passiveInfo?.userID" @submit="handleRejectSubmit"
      @cancel="rejectModalVisible = false" />
  </div>
</template>

<style scoped>
:deep(.ant-select-selection-item) {
  text-align: left;
}
</style>
