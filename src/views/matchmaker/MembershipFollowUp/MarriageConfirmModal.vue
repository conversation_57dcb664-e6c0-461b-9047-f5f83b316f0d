<template>
  <Modal
    v-if="marriageActions.confirmDialogVisible"
    title="解除结婚"
    :visible="marriageActions.confirmDialogVisible"
    :maskClosable="false"
    :width="400"
    @cancel="marriageActions.confirmDialogVisible = false"
  >
    <p v-if="marriageActions.currentRecord">
      是否解除{{ marriageActions.currentRecord.userInfo?.userID }}与{{ marriageActions.currentRecord.passiveInfo?.userID }}的结婚关系？
    </p>
    <template #footer>
      <Button @click="marriageActions.confirmDialogVisible = false">取消</Button>
      <Button
        type="primary"
        :loading="marriageActions.loading"
        @click="handleConfirmDelete"
      >
        确定
      </Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
/**
 * 解除结婚确认弹窗组件
 * 用于确认是否解除结婚关系
 */
import { Button, Modal } from 'ant-design-vue';
import { useMarriageActions } from './hooks/useMarriageActions';

// 使用结婚相关操作的hooks
const marriageActions = useMarriageActions();

// 确认删除结婚关系
const handleConfirmDelete = async () => {
  await marriageActions.confirmDelete();
};
</script>

<style scoped>
/* 样式可以根据需要添加 */
</style>
