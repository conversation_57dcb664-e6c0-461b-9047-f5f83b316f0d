<template>
  <Modal v-model:visible="visible" title="撤销二见" :confirm-loading="loading" @ok="handleConfirm" @cancel="handleCancel"
    :get-container="false">
    <p>是否撤销本次二见录入？</p>
  </Modal>
</template>

<script setup lang="ts">
/**
 * 撤销二见确认弹窗组件
 * 用于确认撤销二见操作
 */
import { computed } from 'vue';
import { Modal } from 'ant-design-vue';
import { useSecondMeetActions } from './hooks/useSecondMeetActions';

// 使用二见相关操作的hooks
const secondMeetActions = useSecondMeetActions();

// 从hooks中获取状态和方法
const visible = computed({
  get: () => secondMeetActions.cancelConfirmVisible,
  set: (val) => { secondMeetActions.cancelConfirmVisible = val; }
});

const loading = computed(() => secondMeetActions.loading);

// 确认撤销
const handleConfirm = async () => {
  await secondMeetActions.confirmCancelSecondMeet();
};

// 取消操作
const handleCancel = () => {
  secondMeetActions.cancelConfirmVisible = false;
};
</script>
