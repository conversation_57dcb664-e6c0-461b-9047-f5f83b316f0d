# 会员跟进功能设计文档

## 功能概述
会员跟进功能用于展示和管理红娘名下会员与被动方的关系进展，包括推荐、反馈、一见、交往、恋爱、结婚等各个阶段的状态和操作。通过该功能，红娘可以全面了解会员关系的发展情况，及时跟进并提供相应服务。

## 数据结构

### 主要数据类型
```typescript
// 表格记录类型
type TableRecord = Required<Required<postV3MembershipFollowUpListResponse>['data']>['list'][number];

// 查询参数 - 直接使用postV3MembershipFollowUpListRequest类型
// 包含以下字段：
// - page: 页码
// - size: 每页条数
// - orderBy: 排序字段
// - orderType: 排序方式
// - adminID: 红娘ID
// - orgIDs: 组织架构ID
// - followUpStatus: 跟进状态
// - userID: 会员ID
// - passiveID: 被动方ID
// - membershipStage: 会员阶段
// - userFeedbacks: 会员反馈
// - passiveFeedbacks: 被动方反馈
// - userMeetFeedbacks: 会员一见反馈
// - passiveMeetFeedbacks: 被动方一见反馈
// - progressDurationMin: 进展停留时间最小值
// - progressDurationMax: 进展停留时间最大值
// - progess: 当前进展
```

## 组件设计

### 主组件: MembershipFollowUp.vue
- 负责整体页面布局和数据获取
- 包含筛选表单和数据表格
- 处理排序、分页等功能

### 子组件
1. **AppointmentFirstMeet.vue**
   - 预约一见弹窗组件
   - 展示双方见面态度和预约表单
   - 允许红娘为会员和被动方预约见面时间

2. **AppointmentCell.vue**
   - 预约一见单元格组件
   - 用于展示预约一见的状态和操作
   - 根据不同状态显示不同内容：
     - 未预约：显示"预约一见"文本和编辑图标
     - 被动方已提供时间：显示时钟图标（悬浮时提示"被动方已提供见面时间"）、"预约一见"文本和编辑图标
     - 已预约：显示对勾图标、预约时间和删除图标（有权限时）
     - 已一见：显示"已一见"标签
   - 点击删除图标时，弹出Element Plus的MessageBox确认对话框询问"是否取消本次见面预约？"
   - 用户确认后，调用接口取消预约
   - 无论接口调用成功还是失败，确认弹窗都会关闭

3. **UserNameCell.vue**
   - 用户姓名单元格组件
   - 用于展示用户姓名、性别和昵称
   - 支持会员和被动方两种类型

4. **UserIdCell.vue**
   - 用户ID单元格组件
   - 用于展示用户ID并支持点击跳转到用户详情
   - 支持会员和被动方两种类型

5. **ProgressCell.vue**
   - 当前进展单元格组件
   - 用于展示当前进展和微信交换状态
   - 使用config.selectionEnum.membershipProgressEnum枚举数据显示进展的label
   - 根据不同的进展状态显示不同的背景颜色和文字颜色
   - 当已交换微信时显示微信图标

6. **MeetCell.vue**
   - 一见单元格组件
   - 用于展示一见状态和操作
   - 使用模板渲染而非JSX/h函数
   - 通过inject获取useMeetActions提供的方法和状态
   - 直接调用hooks中的方法处理确认一见和撤销一见的操作
   - 包含CancelMeetModal组件，通过v-if条件渲染
   - ReportMeeting组件移到了父组件
   - 根据不同权限和状态显示不同内容：
     - 研发+质检（红娘用）权限：
       - 有一见时间：显示时间和删除图标（用于撤销）
       - 无一见时间：显示确认一见按钮和编辑图标
     - 其他权限：
       - 有一见时间：只显示时间
       - 无一见时间：显示占位符"-"

7. **CancelMeetConfirm.vue**
   - 撤销一见确认弹窗组件
   - 使用模板实现确认弹窗，而非 Modal.confirm
   - 支持撤销一见功能，调用 setAchievement 接口

8. **FeedbackCell.vue**
   - 反馈单元格组件
   - 用于展示和编辑会员或被动方的推卡反馈
   - 支持选择"愿意接触"、"拒绝接触"等反馈状态
   - 集成拒绝原因弹窗

9. **FeedbackRejectModal.vue** (来自TablesCardComps)
   - 反馈拒绝原因弹窗组件
   - 用于记录会员或被动方拒绝接触的原因
   - 支持选择多个拒绝原因

10. **RelationshipActions.vue**
   - 关系操作组件
   - 包含跟进状态切换功能
   - 通过下拉菜单提供更多操作选项
   - 集成了AnalysisAction、VideoDateAction和WechatAction组件

11. **VideoDateAction.vue**
   - 视频约会按钮组件
   - 用于显示视频约会按钮并处理点击事件
   - 从RelationshipActions组件中提取出来，实现功能模块化
   - 接收record属性，用于获取用户信息
   - 使用useVideoDate hooks获取视频约会功能
   - 通过inject/provide模式与MakeDate组件通信
   - 根据achievement字段判断是否已视频约会，显示不同状态的按钮
   - 已视频约会时显示禁用的"已视频约会"按钮

12. **RelationshipCell.vue**
   - 交往关系状态展示组件
   - 用于展示交往关系状态
   - 支持确认和解除交往关系操作
   - 实现交往关系显示逻辑：
     - 用户处于交往关系中：显示开始时间+天数+删除按钮
     - 用户不在交往关系中且没有婚姻/恋爱关系，但有历史交往记录：显示时间+已解除标签+编辑按钮
     - 用户不在交往关系中但有婚姻/恋爱关系：显示时间+编辑按钮
     - 用户从未有过交往关系：显示确认交往按钮

13. **LoveRelationshipCell.vue**
   - 恋爱关系状态展示组件
   - 用于展示恋爱关系状态
   - 支持确认和解除恋爱关系操作
   - 实现四种恋爱状态的展示逻辑：
     - 状态1: isInLove=true, isUserConfirmLove=true - 显示恋爱时间+天数+已确认标签+编辑和删除按钮
     - 状态2: isInLove=true, isUserConfirmLove=false - 显示恋爱时间+天数+未确认标签+编辑和删除按钮
     - 状态3: isInLove=false, isInMarriage=false, 有lastLoveStartTime - 显示时间+已解除标签+编辑按钮
     - 状态4: isInLove=false, 无lastLoveStartTime - 显示确认恋爱按钮

14. **MarriageRelationshipCell.vue**
   - 结婚关系状态展示组件
   - 用于展示结婚关系状态
   - 支持确认和解除结婚关系操作
   - 实现三种结婚状态的展示逻辑：
     - 状态1: isInMarriage=true - 显示结婚时间+删除按钮
     - 状态2: isInMarriage=false, 有lastMarriageStartTime - 显示时间+已解除标签+编辑按钮
     - 状态4: isInMarriage=false, 无lastMarriageStartTime - 显示确认结婚按钮+编辑图标
   - 点击确认结婚按钮或编辑按钮打开 ReportMarried 组件，需要上传结婚证
   - 点击删除按钮弹出确认对话框（使用模板实现），确认后调用 setAchievement 接口解除结婚关系

15. **RelationshipForm.vue**
   - 关系确认表单组件
   - 用于确认或解除交往、恋爱、结婚等关系
   - 支持填写关系开始时间和备注信息

16. **CancelMeetModal.vue**
   - 撤销见面确认弹窗组件
   - 使用模板实现确认弹窗
   - 通过inject获取useMeetActions提供的方法和状态
   - 只在需要时通过v-if条件渲染，保持实例唯一性

17. **AnalysisAction.vue**
   - 契合分析按钮组件
   - 用于显示契合分析按钮并处理点击事件
   - 从RelationshipActions组件中提取出来，实现功能模块化
   - 接收record属性，用于获取用户ID和被动方ID
   - 点击按钮打开契合分析页面

18. **useMeetActions.ts**
   - 一见相关操作的hooks
   - 使用provide/inject模式实现跨组件共享
   - 封装确认一见和撤销一见的逻辑
   - 提供openConfirmMeet、openCancelMeet和confirmCancelMeet等方法
   - 管理撤销一见确认弹窗的状态和ReportMeeting组件引用
   - 父组件通过createMeetActions创建并提供上下文
   - 子组件通过useMeetActions获取上下文

19. **useVideoDate.ts**
   - 视频约会相关操作的hooks
   - 使用provide/inject模式实现跨组件共享
   - 封装视频约会的逻辑
   - 提供openVideoDate方法和makeDateRef引用
   - 管理MakeDate组件的状态和引用
   - 父组件通过createVideoDateActions创建并提供上下文
   - 子组件通过useVideoDate获取上下文

20. **useWechatExchange.ts**
   - 微信交换相关操作的hooks
   - 使用provide/inject模式实现跨组件共享
   - 封装微信交换的逻辑
   - 提供openWechatExchange和revokeWechatExchange方法
   - 管理MakeDate组件的状态和引用
   - 处理信息不完整的情况，显示提示弹窗
   - 父组件通过createWechatExchangeActions创建并提供上下文
   - 子组件通过useWechatExchange获取上下文
   - 与WechatInfoModal组件配合使用，显示信息不完整提示

21. **WechatAction.vue**
   - 微信操作按钮组件
   - 用于显示微信录入/撤销按钮并处理点击事件
   - 从RelationshipActions组件中提取出来，实现功能模块化
   - 接收record属性，用于获取用户信息
   - 使用useWechatExchange hooks获取微信交换功能
   - 根据achievement字段判断是否已交换微信，显示不同的按钮文本
   - 已交换微信时显示"撤销微信交换录入"，未交换微信时显示"微信"
   - 通过inject/provide模式与MakeDate组件通信

22. **WechatInfoModal.vue**
   - 微信交换信息不完整弹窗组件
   - 用于显示用户信息不完整时的提示
   - 接收visible和record属性，支持Ref类型
   - 提供close和edit事件
   - 使用defineModel实现visible属性的双向绑定
   - 使用计算属性处理Ref类型的record属性
   - 根据userInfo.canExchangeWechat和passiveInfo.canExchangeWechat判断信息是否完整
   - 显示userInfo.notCompleteFields和passiveInfo.notCompleteFields字段
   - 提供"关闭"、"去编辑会员资料"和"去编辑被动方资料"按钮
   - 与精选列表的微信交换提示弹窗保持一致

## 状态管理
- 单组件内的状态直接声明在组件内，使用Vue的响应式系统管理
- 每个组件负责管理自己的状态和API调用
- 组件通过props和emits进行通信
- 使用ref和reactive管理响应式状态
- 使用computed计算派生状态
- 使用watch监听状态变化

## 交互设计
1. **表格交互**
   - 双击ID进入用户详情页
   - 点击姓名查看真实姓名
   - 表格支持排序和分页
   - 表格支持按多种条件筛选
   - 表格所有单元格内容居中显示

2. **操作交互**
   - 预约一见：点击按钮打开预约弹窗，选择预约时间
   - 确认一见：点击按钮确认会员和被动方已见面，选择见面时间
   - 跟进状态：切换跟进中/放弃跟进状态
   - 反馈编辑：选择会员或被动方对推荐的反馈
   - 关系管理：确认或解除交往、恋爱、结婚等关系
   - 更多操作：通过下拉菜单提供额外功能（契合分析、视频约会、微信录入等）

## API接口
- `postV3MembershipFollowUpList`: 获取会员跟进列表数据
- `setAchievement`: 设置成就（预约一见、确认一见等）

## 权限控制
- 根据用户角色控制可见内容和操作权限
- 研发、质检（红娘用）等角色有特殊权限：
  - 确认一见和撤销一见功能
  - 组织架构和红娘筛选功能
- 服务主管和服务经理可以查看红娘筛选
- 服务经理、研发、质检和管理员可以查看组织架构筛选

## 展示规则
详细的展示规则见需求文档，包括：
- 当前进展状态判断规则（21个阶段，从未推给被动方到结婚）
- 微信图标显示规则（双方已交换微信时显示）
- 各种时间计算规则（进展停留时间、恋爱天数等）
- 各种状态的展示逻辑（预约一见、一见、交往、恋爱、结婚等）
- 反馈状态展示（愿意接触、拒绝接触、未反馈等）
- 牵线进展展示：
  - 空：未开始牵线
  - 牵线中（已牵N天）：正在牵线中，已牵N天显示为红色
  - 牵线成功：牵线已成功
  - 牵线失败：牵线已失败

## 性能优化
- 使用分页加载数据，避免一次加载过多数据
- 使用表格列固定宽度，提高渲染性能
- 按需加载组件，减少初始加载时间
- 使用computed缓存计算结果，避免重复计算
