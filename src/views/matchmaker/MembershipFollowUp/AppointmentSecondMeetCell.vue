<script setup lang="ts">
/**
 * 预约二见单元格组件
 * 用于展示预约二见的状态和操作
 * 
 * 显示规则：
 * 1. 空：没有确认一见
 * 2. 预约二见+编辑icon：(同时满足以下全部)
 *    a. 已确认一见
 *    b. 双方没有预约二见
 *    c. 没有确认二见时
 * 3. 已预约icon+预约时间+取消icon：双方已预约二见，没有确认二见
 * 4. 显示"已二见"：已确认二见
 */
import { Button, message, Tooltip } from 'ant-design-vue';
import { ElMessageBox } from 'element-plus';
import { DeleteOutlined, EditOutlined, ClockCircleOutlined } from '@ant-design/icons-vue';
import { adminRights, postV3Achievement } from '@qianshou/service';
import { dayjsMinutes } from '@qianshou/common';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord;
}>();

const emit = defineEmits(['openAppointmentSecond', 'update']);

// 打开预约二见弹窗
const openAppointmentSecond = () => {
  emit('openAppointmentSecond', props.record);
};

// 撤销预约二见
const cancelAppointmentSecond = async () => {
  try {
    // 确定用户ID和被动方ID
    const userId = props.record.userInfo?.userID;
    const passiveId = props.record.passiveInfo?.userID;

    if (!userId || !passiveId) {
      message.error('缺少必要的用户信息');
      return;
    }

    // 显示确认弹窗
    await ElMessageBox.confirm(
      "是否取消本次二见预约？",
      "取消预约",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    // 调用接口撤销预约二见
    await postV3Achievement({
      userId: userId,
      otherUserId: passiveId,
      serviceType: "revoke-setUpMeeting-second"
    });

    message.success('撤销预约二见成功');
    emit('update');
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error !== 'cancel') {
      console.error('撤销预约二见失败:', error);
      message.error('撤销预约二见失败');
    }
  }
};
</script>

<template>
  <div>
    <!-- 如果没有确认一见，显示空 -->
    <template v-if="!record.meetTime">
      -
    </template>

    <!-- 如果已经二见 -->
    <span v-else-if="record.secondMeetTime">已二见</span>

    <!-- 如果有预约二见时间 -->
    <span v-else-if="record.appointSecondMeetTime">
      <span class="appointment-tag">预</span>
      {{ dayjsMinutes(record.appointSecondMeetTime) }}
      <Button type="link" size="small" @click="cancelAppointmentSecond" style="padding: 0 4px" title="撤销预约">
        <DeleteOutlined style="color: #f50" />
      </Button>
    </span>

    <!-- 如果已确认一见但没有预约二见 -->
    <span v-else>
      <Button type="link" size="small" @click="openAppointmentSecond" style="padding: 0 4px">
        预约二见
        <EditOutlined />
      </Button>
    </span>
  </div>
</template>

<style scoped>
.appointment-tag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 8px;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
}
</style>
