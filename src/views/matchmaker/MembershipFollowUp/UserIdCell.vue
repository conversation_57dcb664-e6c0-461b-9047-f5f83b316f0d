<script setup lang="ts">
/**
 * 用户ID单元格组件
 * 用于展示用户ID并支持点击跳转到用户详情
 */
import { computed } from 'vue';
import { openDetailTab } from '@/utils/open';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord;
  type: 'user' | 'passive'; // 用户类型：会员或被动方
}>();

// 获取用户ID
const userId = computed(() => {
  return props.type === 'user' ? props.record.userInfo?.userID : props.record.passiveInfo?.userID;
});

// 点击ID跳转到用户详情
const handleClick = () => {
  if (userId.value) {
    openDetailTab(String(userId.value));
  }
};
</script>

<template>
  <div @dblclick="handleClick">
    {{ userId }}
  </div>
</template>

<style scoped>

</style>
