<script setup lang="ts">
/**
 * 用户姓名单元格组件
 * 用于展示用户姓名、性别和昵称
 */
import { computed } from 'vue';
import { ManOutlined, WomanOutlined } from '@ant-design/icons-vue';
import { NameMask } from '@qianshou/ui';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord;
  type: 'user' | 'passive'; // 用户类型：会员或被动方
}>();

// 获取用户信息
const userInfo = computed(() => {
  return props.type === 'user' ? props.record.userInfo : props.record.passiveInfo;
});

// 获取性别
const gender = computed(() => {
  return userInfo.value?.gender;
});

// 获取真实姓名
const realName = computed(() => {
  return userInfo.value?.name;
});

// 获取昵称
const nickName = computed(() => {
  return userInfo.value?.nickName;
});
</script>

<template>
  <div class="user-name-cell">
    <!-- 性别图标 -->
    <component
      :is="gender === 'male' ? ManOutlined : WomanOutlined"
      :style="{
        color: gender === 'male' ? '#1890ff' : '#eb2f96',
        marginRight: '4px',
        fontSize: '16px'
      }"
    />

    <!-- 姓名（带掩码） -->
    <NameMask style="min-width:42px">{{ realName }}</NameMask>

    <!-- 昵称（括号内） -->
    <span style="margin-left: 4px">({{ nickName || '' }})</span>
  </div>
</template>

<style scoped>
.user-name-cell {
  display: flex;
  align-items: center;
}
</style>
