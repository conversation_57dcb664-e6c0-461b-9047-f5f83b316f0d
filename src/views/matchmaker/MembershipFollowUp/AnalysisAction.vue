<template>
  <div class="analysis-action">
    <AIMatchAnalysisButton :otherUserId="props.record.passiveInfo?.userID" />
  </div>
</template>

<script setup lang="ts">

import type { TableRecord } from './types';
import { AIMatchAnalysisButton } from "@qianshou/component";

const props = defineProps<{
  record: TableRecord;
}>();
</script>

<style scoped>
.analysis-action {
  display: inline-flex;
  align-items: center;
}

.analysis-action button {
  padding: 0 8px;
}
</style>