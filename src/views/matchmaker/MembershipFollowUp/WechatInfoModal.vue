<script setup lang="ts">
/**
 * 微信交换信息不完整弹窗组件
 * 用于显示用户信息不完整时的提示
 */
import { Button, Modal } from 'ant-design-vue';
import { computed } from 'vue';
import { openDetailTab } from '@/utils/open';
import type { TableRecord } from './types';

const props = defineProps<{
  record: TableRecord | null;
}>();

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>('visible', { default: false });

const emit = defineEmits(['close', 'edit']);

// 计算属性：会员信息是否完整
const isUserInfoComplete = computed(() => {
  return props.record?.userInfo?.canExchangeWechat || false;
});

// 计算属性：被动方信息是否完整
const isPassiveInfoComplete = computed(() => {
  return props.record?.passiveInfo?.canExchangeWechat || false;
});

// 计算属性：会员未完成字段
const userNotCompleteFields = computed(() => {
  return props.record?.userInfo?.notCompleteFields || [];
});

// 计算属性：被动方未完成字段
const passiveNotCompleteFields = computed(() => {
  return props.record?.passiveInfo?.notCompleteFields || [];
});

// 计算属性：会员名称
const userName = computed(() => {
  return props.record?.userInfo?.name || '';
});

// 计算属性：被动方名称
const passiveName = computed(() => {
  return props.record?.passiveInfo?.name || '';
});

// 计算属性：会员ID
const userId = computed(() => {
  return props.record?.userInfo?.userID;
});

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  emit('close');
};

// 编辑会员资料
const handleEditUser = () => {
  if (userId.value) {
    openDetailTab(String(userId.value));
    visible.value = false;
    emit('edit');
  }
};

// 编辑被动方资料
const handleEditPassive = () => {
  const passiveId = props.record?.passiveInfo?.userID;
  if (passiveId) {
    openDetailTab(String(passiveId));
    visible.value = false;
    emit('edit');
  }
};
</script>

<template>
  <Modal
    title="提示"
    :zIndex="2000"
    width="500px"
    v-model:visible="visible"
    :closable="false"
    :footer="null"
  >
    <p v-if="record">
      <template v-if="!isUserInfoComplete">
        {{ userName }}因
        <span style="color:red">{{ userNotCompleteFields.join("、") }}</span>
        未填写，
      </template>
      <template v-if="!isPassiveInfoComplete">
        {{ passiveName }}因
        <span style="color:red">{{ passiveNotCompleteFields.join("、") }}</span>
        未填写，
      </template>
      无法被标记为微信交换。请补充后再标记。
    </p>
    <p class="modal-btn">
      <Button type="default" @click="handleClose">关闭</Button>
      <template v-if="!isUserInfoComplete">
        <Button
          type="primary"
          @click="handleEditUser"
        >
          去编辑会员资料
        </Button>
      </template>
      <template v-if="!isPassiveInfoComplete">
        <Button
          type="primary"
          @click="handleEditPassive"
        >
          去编辑被动方资料
        </Button>
      </template>
    </p>
  </Modal>
</template>

<style scoped>
.modal-btn {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
