<template>
  <div class="inappropriate-feedback-container">
    <div class="page-header">
      <h2>反馈列表</h2>
    </div>

    <div class="table-container">

      <div class="filter">
        <UserSelect size="small" v-model="filters.userID" placeholder="请输入正确用户ID" style="width: 200px" />
        <StaffSelect multiple v-model="filters.matchmakerID" placeholder="请选择红娘" style="width: 260px" />
        <a-button type="primary" @click="search" size="small">搜索</a-button>
        <a-button style="margin-left: 8px" @click="resetFilters" size="small">重置</a-button>
      </div>

      <a-table :columns="columns" :data-source="processedList" :loading="loading" bordered size="small"
        :row-key="(record: any) => record.key" :pagination="false" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'submitTime'">
            {{ record.submitTime ? dayjsMinutes(record.submitTime) : '-' }}
          </template>
          <template v-if="column.key === 'content'">
            {{ record.content || '-' }}
          </template>
          <template v-if="column.key === 'action'">
            <Button v-if="record.msgs?.length" type="link" @click="viewDetailByIndex(record)">详情</Button>
            <span v-else>-</span>
          </template>
        </template>
      </a-table>

      <div class="pagination-container">
        <a-pagination :current="pagination.page" :pageSize="pagination.size" :total="total" :showSizeChanger="true"
          :pageSizeOptions="['10', '20', '50', '100']" :showTotal="(t: number) => `共${t}条`" @change="handlePageChange"
          @showSizeChange="handleSizeChange" />
      </div>
    </div>

    <AIInapproFeedbackDetail v-model="detailVisible" :content="currentDetail" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAIInapproFeedback, type InappropriateItem } from './useAIInapproFeedback';
import AIInapproFeedbackDetail from './AIInapproFeedbackDetail.vue';
import { dayjsMinutes } from '@qianshou/common';
import { Button, Form, Input } from 'ant-design-vue';
import { UserSelect } from '@qianshou/component';
import { openDetailTab } from '@/utils/open';
import { StaffSelect } from '@qianshou/component';

// 使用hook获取数据和方法
const {
  list,
  total,
  loading,
  pagination,
  filters,
  currentDetail,
  detailVisible,
  viewDetail,
  handlePageChange,
  handleSizeChange,
  search,
  resetFilters
} = useAIInapproFeedback();



// 处理数据，将inappropriateContent数组展开为多行
const processedList = computed(() => {
  const result: any[] = [];

  list.value.forEach((item, index) => {
    // 如果inappropriateContent为空或长度为0，创建一个空记录行
    if (!item.inappropriateContent || item.inappropriateContent.length === 0) {
      result.push({
        ...item,
        key: `${item.userID}-${index}-0`,
        rowSpan: 1,
        contentIndex: -1,
        submitTime: null,
        content: null,
        msgs: []
      });
      return;
    }

    // 否则，为每个inappropriateContent创建一行
    const contentLength = item.inappropriateContent.length;
    item.inappropriateContent.forEach((content, contentIndex) => {
      result.push({
        ...item,
        key: `${item.userID}-${index}-${contentIndex}`,
        rowSpan: contentIndex === 0 ? contentLength : 0,
        contentIndex,
        submitTime: content.submitTime,
        content: content.content,
        aiReply: content.aiReply,
        lastSeq: content.lastSeq,
        msgs: content.msgs || []
      });
    });
  });

  return result;
});

// 定义表格列
const columns = [
  {
    title: '用户ID',
    dataIndex: 'userID',
    key: 'userID',
    width: 100,
    customCell: (record: any) => {
      return {
        rowSpan: record.rowSpan,
        onDblclick: () => {
          if (record.userID) {
            openDetailTab(String(record.userID));
          }
        }
      };
    }
  },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
    customCell: (record: any) => {
      return {
        rowSpan: record.rowSpan
      };
    }
  },
  {
    title: '红娘ID',
    dataIndex: 'matchMakerID',
    key: 'matchMakerID',
    width: 100,
    customCell: (record: any) => {
      return {
        rowSpan: record.rowSpan,
      };
    }
  },
  {
    title: '红娘姓名',
    dataIndex: 'matchMakerName',
    key: 'matchMakerName',
    width: 120,
    customCell: (record: any) => {
      return {
        rowSpan: record.rowSpan
      };
    }
  },
  {
    title: '上报时间',
    key: 'submitTime',
    width: 150
  },
  {
    title: '上报内容',
    key: 'content',
    width: 350
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 80
  }
];



/**
 * 根据展开后的记录查看详情
 * @param record 展开后的记录
 */
const viewDetailByIndex = (record: any) => {
  // 直接使用记录中的 msgs 数据
  if (record.msgs && record.msgs.length > 0) {
    // 构造一个 InappropriateContent 对象
    const contentItem = {
      content: record.content,
      aiReply: record.aiReply,
      submitTime: record.submitTime,
      msgs: record.msgs,
      lastSeq: record.lastSeq
    };

    // 使用 viewDetail 函数传递内容项
    viewDetail(contentItem);
  }
};
</script>

<style scoped>
.inappropriate-feedback-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.filter {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 40px;
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.ant-table-thead > tr > th) {
  text-align: center;
  padding: 8px 4px;
}

:deep(.ant-table-tbody > tr > td) {
  text-align: center;
  padding: 8px 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
