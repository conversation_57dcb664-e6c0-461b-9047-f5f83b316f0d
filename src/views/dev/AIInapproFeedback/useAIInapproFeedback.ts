import { ref, reactive } from 'vue';
import {
  getV3SopTrainingAutoReplyInappropriateMessages,
  type GetV3SopTrainingAutoReplyInappropriateMessagesResponse,
  type GetV3SopTrainingAutoReplyInappropriateMessagesRequest
} from '@qianshou/service';
import { message } from 'ant-design-vue';

// 定义列表项类型
export type InappropriateItem = NonNullable<NonNullable<GetV3SopTrainingAutoReplyInappropriateMessagesResponse['data']>['list']>[number];

// 定义不当内容类型
export type InappropriateContent = NonNullable<InappropriateItem['inappropriateContent']>[number];

// 定义消息类型
export type MessageItem = NonNullable<InappropriateContent['msgs']>[number];

/**
 * 不当言论反馈Hook
 * @returns
 */
export const useAIInapproFeedback = () => {
  // 列表数据
  const list = ref<InappropriateItem[]>([]);
  // 总数
  const total = ref(0);
  // 加载状态
  const loading = ref(false);
  // 分页参数
  const pagination = reactive({
    page: 1,
    size: 10
  });

  // 筛选参数
  const filters = reactive({
    userID: undefined as string | undefined,
    matchmakerID: undefined as string[] | undefined
  });
  // 当前选中的详情项
  const currentDetail = ref<InappropriateContent | null>(null);
  // 详情弹窗可见性
  const detailVisible = ref(false);

  /**
   * 获取列表数据
   */
  const getList = async () => {
    loading.value = true;
    try {
      const params: GetV3SopTrainingAutoReplyInappropriateMessagesRequest = {
        page: pagination.page,
        size: pagination.size,
        userID: filters.userID ? [filters.userID] : undefined,
        matchmakerID: filters.matchmakerID
      };

      // 使用真实API调用
      const response = await getV3SopTrainingAutoReplyInappropriateMessages(params);

      if (response?.data?.list) {
        list.value = response.data.list;
        total.value = response.data.total || 0;
      } else {
        list.value = [];
        total.value = 0;
      }
    } catch (error) {
      console.error('获取不当言论列表失败:', error);
      message.error('获取不当言论列表失败');
      list.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 查看详情
   * @param item 不当内容项
   */
  const viewDetail = (item: InappropriateContent) => {
    currentDetail.value = item;
    detailVisible.value = true;
  };

  /**
   * 关闭详情
   */
  const closeDetail = () => {
    detailVisible.value = false;
    currentDetail.value = null;
  };

  /**
   * 处理分页变化
   * @param page
   */
  const handlePageChange = (page: number) => {
    pagination.page = page;
    getList();
  };

  /**
   * 处理每页条数变化
   * @param _current 当前页码（未使用）
   * @param size 每页条数
   */
  const handleSizeChange = (_current: number, size: number) => {
    pagination.size = size;
    pagination.page = 1;
    getList();
  };

  /**
   * 处理分页组件事件
   * @param param
   */
  const handlePagination = (param: { page: number; limit: number }) => {
    pagination.page = param.page;
    pagination.size = param.limit;
    getList();
  };

  /**
   * 搜索
   */
  const search = () => {
    pagination.page = 1;
    getList();
  };

  /**
   * 重置筛选器
   */
  const resetFilters = () => {
    filters.userID = undefined;
    filters.matchmakerID = undefined;
    pagination.page = 1;
    getList();
  };

  // 初始化时获取列表
  getList();

  return {
    list,
    total,
    loading,
    pagination,
    filters,
    currentDetail,
    detailVisible,
    getList,
    viewDetail,
    closeDetail,
    handlePageChange,
    handleSizeChange,
    handlePagination,
    search,
    resetFilters
  };
};
