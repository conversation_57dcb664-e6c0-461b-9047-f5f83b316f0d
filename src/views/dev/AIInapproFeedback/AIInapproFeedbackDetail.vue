<template>
  <a-modal title="反馈详情" :visible="modelValue" width="800px" @cancel="handleClose" :footer="null" :destroyOnClose="true">
    <div class="inappropriate-detail">
      <a-empty v-if="!content" description="暂无详情数据" />
      <template v-else>
        <div class="chat-container" v-if="content?.msgs?.length">
          <div v-for="(msg, msgIndex) in content.msgs" :key="msgIndex" class="wechat-detail">
            <div :class="{ 'sender-right': msg.senderRole === 2 }">
              <div class="sender-name">
                {{ msg.senderName }} ({{ msg.senderRole === 1 ? '客户' : '红娘' }})
                {{ dayjsMilliseconds(msg.msgTime) }}
              </div>
              <div class="msg">
                <text-msg :right="msg.senderRole === 2">
                  {{ msg.msg?.content || '' }}
                </text-msg>
              </div>
            </div>
          </div>
        </div>
        <a-alert v-if="content?.content" type="success" style="margin-bottom: 10px;">
          <template #message>
            <div class="alert-title">红娘填写的回复:</div>
          </template>
          <template #description>
            <div class="alert-content">{{ content.content }}</div>
          </template>
        </a-alert>

        <a-alert v-if="content?.aiReply" type="error" style="margin-bottom: 10px;">
          <template #message>
            <div class="alert-title">AI生成的回复:</div>
          </template>
          <template #description>
            <div class="alert-content">{{ content.aiReply }}</div>
          </template>
        </a-alert>
      </template>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import TextMsg from "@/components/ChatMessage/TextMsg.vue";
import { dayjsMilliseconds } from '@qianshou/common';
import type { InappropriateContent } from './useAIInapproFeedback';

defineProps<{
  modelValue: boolean;
  content: InappropriateContent | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

const handleClose = () => {
  emit('update:modelValue', false);
};
</script>

<style scoped>
.inappropriate-detail {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
}

.empty-data {
  text-align: center;
  padding: 20px;
  color: #999;
}

.content-item {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.content-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.submit-info {
  margin-bottom: 10px;
  color: #666;
  font-size: 14px;
}

.chat-container {
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
}

.wechat-detail {
  margin-bottom: 10px;
}

.wechat-detail:last-child {
  margin-bottom: 0;
}

.sender-right {
  text-align: right;
}

.sender-right .sender-name {
  text-align: right;
}

.sender-right .msg {
  display: flex;
  justify-content: flex-end;
}

.sender-name {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.msg {
  display: flex;
}

.reply-content {
  background-color: #f0f9eb;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.reply-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #67c23a;
}

.reply-text {
  color: #333;
  white-space: pre-wrap;
}

.alert-title {
  font-weight: bold;
}

.alert-content {
  white-space: pre-wrap;
  color: #333;
}
</style>
