import { computed, type Ref } from 'vue'

/**
 * 录音导航hook
 * @param currInd 当前录音索引
 * @param questions 录音列表
 * @param selectQuestion 选择录音的方法
 * @returns 导航相关的状态和方法
 */
export function useRecordingNavigation(
  currInd: Ref<number>,
  questions: Ref<any[]>,
  selectQuestion: (index: number) => Promise<void>
) {
  // 是否可以导航到上一个录音
  const canNavigatePrev = computed(() => questions.value?.length > 0 && currInd.value > 0)

  // 是否可以导航到下一个录音
  const canNavigateNext = computed(() => questions.value?.length > 0 && currInd.value + 1 < questions.value.length)

  /**
   * 导航到上一个录音
   */
  const navigatePrev = async () => {
    if (canNavigatePrev.value) {
      await selectQuestion(currInd.value - 1)
    }
  }

  /**
   * 导航到下一个录音
   */
  const navigateNext = async () => {
    if (canNavigateNext.value) {
      await selectQuestion(currInd.value + 1)
    }
  }

  return {
    canNavigatePrev,
    canNavigateNext,
    navigatePrev,
    navigateNext
  }
}
