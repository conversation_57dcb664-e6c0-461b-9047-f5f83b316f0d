import { ref } from 'vue'
import { GetV3SopTrainingStandardAnswersRecordingsRequest, config } from '@qianshou/service'

// 筛选配置类型
export interface FilterConfig {
  /** 录音库类型：'sale' 销售录音库 | 'serve' 服务录音库 */
  recordingType?: 'sale' | 'serve'
}

// 扩展查询参数类型以支持一级分类筛选
export interface ExtendedRecordingQuery extends GetV3SopTrainingStandardAnswersRecordingsRequest {
  category?: string[]  // 一级分类字段
}

/**
 * 录音筛选hook
 * @param query 查询参数
 * @param getQuestions 获取录音列表的方法
 * @param filterConfig 筛选配置，用于区分不同录音库的筛选需求
 * @returns 筛选相关的状态和方法
 */
export function useRecordingFilter(
  query: ExtendedRecordingQuery,
  getQuestions: () => Promise<void>,
  filterConfig: FilterConfig = {}
) {
  // 抽屉可见性
  const visible = ref(false)

  // 根据录音库类型确定筛选选项的显示
  const { recordingType = 'sale' } = filterConfig

  // 根据录音类型获取收入选项
  const incomeOptions = recordingType === 'serve'
    ? (config.value?.selectionEnum?.incomeRange || [])
    : [
      { label: '5w+', value: 5 },
      { label: '10w+', value: 10 },
      { label: '50w+', value: 50 },
    ]

  // 性别选项
  const genderOptions = [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' },
  ]

  /**
   * 打开抽屉
   */
  const openDrawer = () => {
    visible.value = true
  }

  /**
   * 关闭抽屉
   */
  const closeDrawer = () => {
    visible.value = false
  }

  /**
   * 搜索
   */
  const search = async () => {
    await getQuestions()
  }

  // 根据录音库类型自动推导筛选选项
  const showCharacteristic = recordingType === 'sale'
  const showCategory = recordingType === 'serve'

  return {
    visible,
    incomeOptions,
    genderOptions,
    openDrawer,
    closeDrawer,
    search,
    // 筛选配置
    filterConfig: {
      recordingType,
      showCharacteristic,
      showCategory
    }
  }
}
