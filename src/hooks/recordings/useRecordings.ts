import { ref, reactive } from 'vue'
import {
  getV3SopTrainingStandardAnswersRecordings,
  getV3SopTrainingStandardAnswersRecording,
  GetV3SopTrainingStandardAnswersRecordingResponse,
  getV3SopTrainingComplaintStandardAnswersRecordings,
  getV3SopTrainingComplaintStandardAnswersRecording,
  GetV3SopTrainingComplaintStandardAnswersRecordingResponse
} from '@qianshou/service'
import { ExtendedRecordingQuery, FilterConfig } from './useRecordingFilter'

/**
 * 录音库数据获取和管理hook
 * @param filterConfig 筛选配置，用于区分不同录音库的查询需求
 * @returns 录音相关的状态和方法
 */
export function useRecordings(filterConfig: FilterConfig = {}) {
  // 根据录音库类型确定初始查询参数
  const { recordingType = 'sale' } = filterConfig

  // 根据录音库类型自动推导筛选选项
  const showCharacteristic = recordingType === 'sale'
  const showCategory = recordingType === 'serve'

  // 查询参数 - 根据配置初始化不同字段
  const query = reactive<ExtendedRecordingQuery>({
    characteristic: showCharacteristic ? undefined : undefined,
    gender: undefined,
    income: undefined,
    source: undefined,
    category: showCategory ? undefined : undefined
  })

  // 录音列表
  const questions = ref<any[]>([])

  // 当前选中的录音索引
  const currInd = ref(0)

  // 当前选中的录音
  const currQues = ref<any>()

  // 当前选中的录音详情 - 支持两种 API 响应类型
  const record = ref<GetV3SopTrainingStandardAnswersRecordingResponse['data'] | GetV3SopTrainingComplaintStandardAnswersRecordingResponse['data']>()

  // 当前选中的录音标签页索引
  const currRecordTab = ref(0)

  /**
   * 获取录音列表
   */
  const getQuestions = async () => {
    if (recordingType === 'serve') {
      // 服务录音库 API 调用
      const apiParams = {
        category: query.category || [],
        gender: query.gender || [],
        income: query.income || [],
        source: query.source || []
      }

      const { data } = await getV3SopTrainingComplaintStandardAnswersRecordings(apiParams)
      questions.value = data?.list ?? []
    } else {
      // 标准录音库 API 调用（销售录音库）
      const apiParams = {
        gender: query.gender,
        income: query.income,
        source: query.source,
        characteristic: query.characteristic
      }

      const { data } = await getV3SopTrainingStandardAnswersRecordings(apiParams)
      questions.value = data?.list ?? []
    }
  }

  /**
   * 初始化
   */
  const init = async () => {
    await getQuestions()
    if (!questions.value[0]?.id) return
    currInd.value = 0
    await selectQuestion(0)
  }

  /**
   * 选择录音
   * @param index 录音索引
   */
  const selectQuestion = async (index: number) => {
    currInd.value = index
    currQues.value = questions.value[index]

    if (recordingType === 'serve') {
      // 服务录音库详情 API 调用
      const { data: recordData } = await getV3SopTrainingComplaintStandardAnswersRecording({ id: questions.value[index]?.id ?? 0 })
      record.value = recordData
    } else {
      // 标准录音库详情 API 调用（销售录音库）
      const { data: recordData } = await getV3SopTrainingStandardAnswersRecording({ id: questions.value[index]?.id ?? '' })
      record.value = recordData
    }

    currRecordTab.value = 0
  }

  return {
    query,
    questions,
    currInd,
    currQues,
    record,
    currRecordTab,
    getQuestions,
    init,
    selectQuestion
  }
}
