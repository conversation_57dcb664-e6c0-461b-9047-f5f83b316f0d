/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【绩效】订单确认-搜索的请求参数 */
export interface GetV3PerformanceOrderConfirmsRequest {
  /**
   * 订单状态，notStarted-未开启，started-已开启，payoff-已发薪
   */
  status?: string[];
  /**
   * 订单支付开始时间，unixNano
   */
  pay_start_time?: number;
  /**
   * 订单支付结束时间，unixNano
   */
  pay_end_time?: number;
  /**
   * 分页页码
   */
  page?: number;
  /**
   * 分页容量
   */
  size?: number;
  /**
   * 用户id
   */
  uid?: number;
  /**
   * 红娘id
   */
  matchmaker_id?: number;
  /**
   * 组织架构id列表
   */
  org_ids?: string[];
  /**
   * 订单开启时间，unixNano
   */
  start_service_time?: number;
}

/** 接口【绩效】订单确认-搜索的响应数据 */
export interface GetV3PerformanceOrderConfirmsResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 数据列表
     */
    list?: {
      id?: number;
      /**
       * 年龄
       */
      age?: number;
      /**
       * 用户id
       */
      uid?: number;
      /**
       * 性别，male/female
       */
      gender?: string;
      /**
       * 订单状态，notStarted-未开启，started-已开启，payoff-已发薪
       */
      status?: string;
      /**
       * 订单id
       */
      order_id?: number;
      /**
       * 支付时间，unix时间
       */
      pay_time?: number;
      /**
       * 用户昵称
       */
      nick_name?: string;
      /**
       * 用户真实姓名
       */
      real_name?: string;
      /**
       * 用户所在地编码
       */
      residence?: string;
      /**
       * 订单金额（单位元）
       */
      order_price?: number;
      /**
       * 归属业绩（单位元）
       */
      performance?: number;
      /**
       * 会员阶段
       */
      serve_stage?: number;
      /**
       * 销售红娘名字
       */
      sale_matchmaker?: string;
      /**
       * 服务红娘名字
       */
      serve_matchmaker?: string;
      /**
       * 订单开启时间，unix时间
       */
      start_service_time?: number;
    }[];
    /**
     * 数据总量
     */
    total?: number;
  };
}

/**
 * 接口【绩效】订单确认-搜索的请求函数
 * 
 * 
 */
export function getV3PerformanceOrderConfirms(d: GetV3PerformanceOrderConfirmsRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3PerformanceOrderConfirmsResponse>({
    url: `/v3/performance/order-confirms`,
    method: 'GET',
    params: {
      status: d.status,
      pay_start_time: d.pay_start_time,
      pay_end_time: d.pay_end_time,
      page: d.page,
      size: d.size,
      uid: d.uid,
      matchmaker_id: d.matchmaker_id,
      org_ids: d.org_ids,
      start_service_time: d.start_service_time
    },
    ...restArg,
  })
}
