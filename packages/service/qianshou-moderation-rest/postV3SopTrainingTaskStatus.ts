/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【培训任务】更新任务状态的请求参数 */
export interface PostV3SopTrainingTaskStatusRequest {
  /**
   * 任务id
   */
  id?: number;
  /**
   * 更新原因
   */
  reason?: string;
  /**
   * 0:待完成；1:完成；2:失败；-1:删除
   */
  status?: number;
  /**
   * 电话培训答案id
   */
  answerRecordID?: number;
}

/** 接口【培训任务】更新任务状态的响应数据 */
export interface PostV3SopTrainingTaskStatusResponse {
  msg?: string;
  code?: number;
}

/**
 * 接口【培训任务】更新任务状态的请求函数
 * 
 * 
 */
export function postV3SopTrainingTaskStatus(d: PostV3SopTrainingTaskStatusRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3SopTrainingTaskStatusResponse>({
    url: `/v3/sop-training/task/status`,
    method: 'POST',
    data: {
      id: d.id,
      reason: d.reason,
      status: d.status,
      answerRecordID: d.answerRecordID
    },
    ...restArg,
  })
}
