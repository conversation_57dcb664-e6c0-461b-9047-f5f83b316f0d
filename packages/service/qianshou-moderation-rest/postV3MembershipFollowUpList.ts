/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口获取会员跟进列表的请求参数 */
export interface PostV3MembershipFollowUpListRequest {
  /**
   * 分页页码
   */
  page?: number;
  /**
   * 分页容量
   */
  size?: number;
  /**
   * 组织架构id列表
   */
  orgIDs?: number[];
  /**
   * 用户id
   */
  userID?: number;
  /**
   * 红娘id
   */
  adminID?: number;
  /**
   * 排序字段
   */
  orderBy?: string;
  /**
   * 当前进展
   */
  progress?: number[];
  /**
   * 排序顺序：生序：asc 倒序：desc
   */
  orderType?: string;
  /**
   * 被动方id
   */
  passiveID?: number;
  /**
   * 会员推荐反馈数组
   */
  userFeedbacks?: string[];
  /**
   * 跟进状态：0:跟进中 -1:已结束
   */
  followUpStatus?: number[];
  /**
   * 会员阶段
   */
  membershipStage?: number[];
  /**
   * 被动方反馈数组
   */
  passiveFeedbacks?: string[];
  /**
   * 会员一见反馈数组
   */
  userMeetFeedbacks?: number[];
  /**
   * 当前进展停留时间最大值(天)
   */
  progressDurationMax?: number;
  /**
   * 当前进展停留时间最小值(天)
   */
  progressDurationMin?: number;
  /**
   * 被动方一见反馈数组
   */
  passiveMeetFeedbacks?: number[];
}

/** 接口获取会员跟进列表的响应数据 */
export interface PostV3MembershipFollowUpListResponse {
  msg?: string;
  code?: number;
  data?: {
    list?: {
      /**
       * 唯一键
       */
      id?: number;
      isInLove?: boolean;
      meetTime?: number;
      progress?: number;
      userInfo?: {
        name?: string;
        gender?: string;
        userID?: number;
        meetTime?: number[];
        nickName?: string;
        meetMethod?: number[];
        serveAdmin?: {
          name?: string;
          adminID?: number;
        };
        contactAdmin?: {
          name?: string;
          adminID?: number;
        };
        meetAttitude?: number;
        passiveAdmin?: {
          name?: string;
          adminID?: number;
        };
        /**
         * 见面信息针对范围： 对此会员/对所有人
         */
        meetInfoScope?: string;
        canExchangeWechat?: boolean;
        notCompleteFields?: string[];
      };
      achievement?: string[];
      passiveInfo?: {
        name?: string;
        gender?: string;
        userID?: number;
        meetTime?: number[];
        nickName?: string;
        meetMethod?: number[];
        serveAdmin?: {
          name?: string;
          adminID?: number;
        };
        contactAdmin?: {
          name?: string;
          adminID?: number;
        };
        meetAttitude?: number;
        passiveAdmin?: {
          name?: string;
          adminID?: number;
        };
        /**
         * 见面信息针对范围： 对此会员/对所有人
         */
        meetInfoScope?: string;
        canExchangeWechat?: boolean;
        notCompleteFields?: string[];
      };
      isInMarriage?: boolean;
      userFeedback?: string;
      followUpStatus?: number;
      /**
       * 二见时间
       */
      secondMeetTime?: number;
      appointMeetTime?: number;
      passiveFeedback?: string;
      isExchangeWeChat?: boolean;
      isInRelationship?: boolean;
      progressDuration?: number;
      /**
       * 会员见面反馈
       */
      userMeetFeedback?: number;
      /**
       * 用户是否确认恋爱：已确认，未确认，未恋爱
       */
      isUserConfirmLove?: string;
      lastLoveStartTime?: number;
      /**
       * 被动方见面反馈
       */
      passiveMeetFeedback?: number;
      recommendToUserTime?: number;
      /**
       * 预约二见时间
       */
      appointSecondMeetTime?: number;
      lastMarriageStartTime?: number;
      passiveContactProgress?: string;
      recommendToPassiveTime?: number;
      /**
       * 会员二见反馈
       */
      userSecondMeetFeedback?: number;
      /**
       * 双向满意时间
       */
      mtuallySatisfactoryTime?: number;
      isPassiveProvideMeetTime?: boolean;
      lastRelationshipStartTime?: number;
      passiveContactCreatedTime?: number;
      /**
       * 被动方二见反馈
       */
      passiveSecondMeetFeedback?: number;
    }[];
    total?: number;
  };
}

/**
 * 接口获取会员跟进列表的请求函数
 * 
 * 
 */
export function postV3MembershipFollowUpList(d: PostV3MembershipFollowUpListRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3MembershipFollowUpListResponse>({
    url: `/v3/membership/follow-up/list`,
    method: 'POST',
    data: {
      page: d.page,
      size: d.size,
      orgIDs: d.orgIDs,
      userID: d.userID,
      adminID: d.adminID,
      orderBy: d.orderBy,
      progress: d.progress,
      orderType: d.orderType,
      passiveID: d.passiveID,
      userFeedbacks: d.userFeedbacks,
      followUpStatus: d.followUpStatus,
      membershipStage: d.membershipStage,
      passiveFeedbacks: d.passiveFeedbacks,
      userMeetFeedbacks: d.userMeetFeedbacks,
      progressDurationMax: d.progressDurationMax,
      progressDurationMin: d.progressDurationMin,
      passiveMeetFeedbacks: d.passiveMeetFeedbacks
    },
    ...restArg,
  })
}
