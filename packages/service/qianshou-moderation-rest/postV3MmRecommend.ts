/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口推荐用户的请求参数 */
export interface PostV3MmRecommendRequest {
  base?: {
    age?: number;
    edu?: string;
    job?: string;
    sign?: string;
    is211?: boolean;
    is985?: boolean;
    avatar?: string;
    carOwn?: number;
    height?: number;
    nation?: string;
    photos?: string[];
    school?: string;
    /**
     * app:app注册的资料 side-user:红娘编辑的信息
     */
    source?: string;
    userID?: number;
    zodiac?: string;
    company?: string;
    homeTown?: string;
    houseOwn?: number;
    kidsTake?: string;
    kidsWant?: string;
    fatherJob?: string;
    incomeMax?: number;
    incomeMin?: number;
    marryPlan?: string;
    motherJob?: string;
    onlyChild?: string;
    religious?: string;
    residence?: string;
    familyType?: string;
    /**
     * 用户上传的生活照
     */
    lifePhotos?: string[];
    description?: string;
    loveHistory?: string;
    IDCardPrefix?: string;
    tattooStatus?: string;
    educationForm?: string;
    maritalStatus?: number;
    smokingStatus?: string;
    singleDuration?: string;
    educationStatus?: number;
    fatherJobStatus?: string;
    motherJobStatus?: string;
    /**
     * 红娘上传的照片
     */
    photosFromMatchmaker?: string[];
  };
  /**
   * 直接推荐转被动方推荐:direct-to-passive
   */
  action?: string;
  /**
   * serve:服务页面 sale:销售页面
   */
  source?: string;
  useVoice?: boolean;
  markingTag?: unknown[];
  aboutFamily?: string;
  aboutHobbies?: string;
  recommendType?: number;
  aboutCharacter?: string;
  /**
   * 卡片样式，红娘精选-matchmakerPremium
   */
  recommendStyle?: string;
  useInterestTag?: boolean;
  recommendReason?: string;
  /**
   * 推荐理由
   */
  curatedRationale?: {
    /**
     * 小标题
     */
    title?: string;
    /**
     * 配图列表
     */
    imageUrl?: string[];
    /**
     * 描述
     */
    description?: string;
  }[];
  /**
   * 是否是自定义卡片推荐
   */
  isCustomRecommend?: boolean;
  recommendedUserID?: number[];
  recommenderUserID?: number;
  /**
   * 重点展示字段（base中字段名列表）
   */
  userSpotlightData?: string[];
  /**
   * 是否保存红娘推荐语
   */
  saveRecommendReason?: boolean;
  /**
   * 对对方态度列表
   */
  attitudeToCounterpart?: {
    /**
     * 小标题
     */
    title?: string;
    /**
     * 详细描述
     */
    description?: string;
  }[];
  /**
   * 是否使用“对对方态度”
   */
  useAttitudeToCounterpart?: boolean;
}

/** 接口推荐用户的响应数据 */
export interface PostV3MmRecommendResponse {
  msg: string;
  code: number;
  data: {
    failedNum?: number;
    failedList?: {
      name?: string;
      realName?: string;
    }[];
    successNum?: number;
    failedReason?: string;
  };
}

/**
 * 接口推荐用户的请求函数
 * 
 * 
 */
export function postV3MmRecommend(d: PostV3MmRecommendRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3MmRecommendResponse>({
    url: `/v3/mm-recommend`,
    method: 'POST',
    data: {
      base: d.base,
      action: d.action,
      source: d.source,
      useVoice: d.useVoice,
      markingTag: d.markingTag,
      aboutFamily: d.aboutFamily,
      aboutHobbies: d.aboutHobbies,
      recommendType: d.recommendType,
      aboutCharacter: d.aboutCharacter,
      recommendStyle: d.recommendStyle,
      useInterestTag: d.useInterestTag,
      recommendReason: d.recommendReason,
      curatedRationale: d.curatedRationale,
      isCustomRecommend: d.isCustomRecommend,
      recommendedUserID: d.recommendedUserID,
      recommenderUserID: d.recommenderUserID,
      userSpotlightData: d.userSpotlightData,
      saveRecommendReason: d.saveRecommendReason,
      attitudeToCounterpart: d.attitudeToCounterpart,
      useAttitudeToCounterpart: d.useAttitudeToCounterpart
    },
    ...restArg,
  })
}
