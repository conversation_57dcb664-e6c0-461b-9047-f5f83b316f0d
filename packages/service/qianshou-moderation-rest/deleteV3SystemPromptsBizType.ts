/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【system-prompt】类型下线的请求参数 */
export interface DeleteV3SystemPromptsBizTypeRequest {
  /**
   * 类型
   */
  bizType: string;
}

/** 接口【system-prompt】类型下线的响应数据 */
export interface DeleteV3SystemPromptsBizTypeResponse {
  msg?: string;
  code?: number;
}

/**
 * 接口【system-prompt】类型下线的请求函数
 * 
 * 
 */
export function deleteV3SystemPromptsBizType(d: DeleteV3SystemPromptsBizTypeRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<DeleteV3SystemPromptsBizTypeResponse>({
    url: `/v3/system-prompts/biz-type`,
    method: 'DELETE',
    params: {
      bizType: d.bizType
    },
    ...restArg,
  })
}
