/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口会员跟进更新状态的请求参数 */
export interface PostV3MembershipFollowUpStatusRequest {
  /**
   * 用户ID
   */
  userID?: number;
  /**
   * 被动方ID
   */
  passiveID?: number;
  /**
   * 状态
   */
  followUpStatus?: number;
}

/** 接口会员跟进更新状态的响应数据 */
export interface PostV3MembershipFollowUpStatusResponse {
  msg?: string;
  code?: number;
}

/**
 * 接口会员跟进更新状态的请求函数
 * 
 * 
 */
export function postV3MembershipFollowUpStatus(d: PostV3MembershipFollowUpStatusRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3MembershipFollowUpStatusResponse>({
    url: `/v3/membership/follow-up/status`,
    method: 'POST',
    data: {
      userID: d.userID,
      passiveID: d.passiveID,
      followUpStatus: d.followUpStatus
    },
    ...restArg,
  })
}
