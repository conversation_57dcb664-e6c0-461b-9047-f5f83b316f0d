/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【广场动态】获取ta的动态的请求参数 */
export interface GetV2UserPostsRequest {
  /**
   * 分页页码
   */
  page: number;
  /**
   * 分页容量
   */
  size: number;
  /**
   * 用户id
   */
  uid: number;
}

/** 接口【广场动态】获取ta的动态的响应数据 */
export interface GetV2UserPostsResponse {
  /**
   * 帖子列表
   */
  posts?: {
    /**
     * 帖子id
     */
    id?: number;
    /**
     * 用户信息
     */
    user?: {
      /**
       * 用户id
       */
      id?: number;
      /**
       * 用户年龄
       */
      age?: number;
      /**
       * 用户昵称
       */
      name?: string;
      /**
       * 用户头像信息
       */
      avatar?: {
        /**
         * 头像url
         */
        url?: string;
        dhash?: number;
        width?: number;
        height?: number;
      };
      /**
       * 用户性别，male/female
       */
      gender?: string;
      /**
       * 用户教育程度
       */
      eduLevel?: number;
      /**
       * 用户居住地编码
       */
      residence?: string;
    };
    /**
     * 帖子图片列表
     */
    images?: {
      /**
       * 图片url
       */
      url?: string;
      dhash?: number;
      width?: number;
      height?: number;
    }[];
    /**
     * 帖子多媒体数据列表
     */
    medias?: {
      /**
       * 视频信息
       */
      video?: {
        /**
         * 视频url
         */
        url?: string;
        /**
         * 封面图
         */
        cover?: {
          url?: string;
          dhash?: number;
          width?: number;
          height?: number;
        };
        dhash?: number;
        width?: number;
        height?: number;
        duration?: number;
      };
      /**
       * 多媒体类型，video/mp4 - 视频
       */
      mediaType?: string;
    }[];
    display?: number;
    /**
     * 评论数量
     */
    commentsNum?: number;
    createdTime?: number;
    /**
     * 帖子内容
     */
    textContent?: string;
    /**
     * 点赞数量
     */
    thumbsUpNum?: number;
  }[];
  /**
   * 帖子总量
   */
  total?: number;
}

/**
 * 接口【广场动态】获取ta的动态的请求函数
 * 
 * 
 */
export function getV2UserPosts(d: GetV2UserPostsRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV2UserPostsResponse>({
    url: `/v2/user/posts`,
    method: 'GET',
    params: {
      page: d.page,
      size: d.size,
      uid: d.uid
    },
    ...restArg,
  })
}
