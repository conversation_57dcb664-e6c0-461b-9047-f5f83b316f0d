/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'


/** 接口【system-prompt】获取类型枚举的响应数据 */
export interface GetV3SystemPromptsBizTypeResponse {
  msg?: string;
  code?: number;
  /**
   * 枚举数组
   */
  data?: {
    /**
     * 展示值
     */
    label: string;
    /**
     * value值
     */
    value: string;
    /**
     * enabled-启动，deprecated-下线
     */
    status: string;
  }[];
}

/**
 * 接口【system-prompt】获取类型枚举的请求函数
 * 
 * 
 */
export function getV3SystemPromptsBizType(restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SystemPromptsBizTypeResponse>({
    url: `/v3/system-prompts/biz-type`,
    method: 'GET',
    ...restArg,
  })
}
