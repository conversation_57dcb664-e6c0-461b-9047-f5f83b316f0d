/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【用户特征分析】头像评分的请求参数 */
export interface GetV3UserFeatureAnalysisAvatarScoreRequest {
  /**
   * 用户id
   */
  userId: number;
}

/** 接口【用户特征分析】头像评分的响应数据 */
export interface GetV3UserFeatureAnalysisAvatarScoreResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 评分
     */
    score?: number;
    /**
     * 评分原因
     */
    reason?: string;
  };
}

/**
 * 接口【用户特征分析】头像评分的请求函数
 * 
 * 
 */
export function getV3UserFeatureAnalysisAvatarScore(d: GetV3UserFeatureAnalysisAvatarScoreRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3UserFeatureAnalysisAvatarScoreResponse>({
    url: `/v3/user-feature-analysis/avatar-score`,
    method: 'GET',
    params: {
      userId: d.userId
    },
    ...restArg,
  })
}
