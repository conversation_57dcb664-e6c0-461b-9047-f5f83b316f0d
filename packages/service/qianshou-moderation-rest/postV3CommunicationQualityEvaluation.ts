/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口聊天质检判断的请求参数 */
export interface PostV3CommunicationQualityEvaluationRequest {
  extra?: {
    /**
     * 被动方牵线时传会员uid
     */
    memberUserID?: number;
  };
  userID?: number;
  /**
   * 业务类型
   */
  businessType?: string;
  /**
   * 物料类型
   */
  materialType?: string[];
}

/** 接口聊天质检判断的响应数据 */
export interface PostV3CommunicationQualityEvaluationResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 被动方牵线
     */
    passiveContact?: {
      /**
       * 准确率
       */
      accuracyRate?: number;
      /**
       * 不准确的项
       */
      qualityIssues?: {
        label?: string;
        value?: string;
        reason?: string;
      }[];
    };
  };
}

/**
 * 接口聊天质检判断的请求函数
 * 
 * materialType枚举：phone 外呼通话；we-com-msg 企微聊天；we-com-voice-call 企微语音通话；
businessType枚举：passive-contact 被动方牵线
 */
export function postV3CommunicationQualityEvaluation(d: PostV3CommunicationQualityEvaluationRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3CommunicationQualityEvaluationResponse>({
    url: `/v3/communication-quality-evaluation`,
    method: 'POST',
    data: {
      extra: d.extra,
      userID: d.userID,
      businessType: d.businessType,
      materialType: d.materialType
    },
    ...restArg,
  })
}
