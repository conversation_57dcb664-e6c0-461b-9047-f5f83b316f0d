/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【客诉安抚录音库】详情的请求参数 */
export interface GetV3SopTrainingComplaintStandardAnswersRecordingRequest {
  /**
   * 客诉安抚录音库ID
   */
  id: number;
}

/** 接口【客诉安抚录音库】详情的响应数据 */
export interface GetV3SopTrainingComplaintStandardAnswersRecordingResponse {
  msg?: string;
  code?: number;
  data?: {
    id?: number;
    /**
     * 总结
     */
    sumUp?: string;
    /**
     * 备注
     */
    detail?: string;
    /**
     * 会员性别
     */
    gender?: string;
    /**
     * 会员收入
     */
    income?: number;
    /**
     * 录音文件
     */
    record?: {
      /**
       * 录音文件地址
       */
      url?: string;
      /**
       * 录音音转文
       */
      text?: string;
    }[];
    /**
     * 会员入库渠道
     */
    source?: string;
    /**
     * 会员入库时间
     */
    relationCreatedTime?: number;
  };
}

/**
 * 接口【客诉安抚录音库】详情的请求函数
 * 
 * 
 */
export function getV3SopTrainingComplaintStandardAnswersRecording(d: GetV3SopTrainingComplaintStandardAnswersRecordingRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingComplaintStandardAnswersRecordingResponse>({
    url: `/v3/sop-training/complaint-standard-answers/recording`,
    method: 'GET',
    params: {
      id: d.id
    },
    ...restArg,
  })
}
