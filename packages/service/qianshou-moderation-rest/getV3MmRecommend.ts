/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【售前快速推荐】推荐卡片数据的请求参数 */
export interface GetV3MmRecommendRequest {
  pToken: string;
}

/** 接口【售前快速推荐】推荐卡片数据的响应数据 */
export interface GetV3MmRecommendResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 来源，serve-服务
     */
    source?: string;
    jumpURL?: string;
    /**
     * 用户基本资料
     */
    userBase?: {
      age?: number;
      edu?: string;
      job?: string;
      is211?: boolean;
      is985?: boolean;
      voice?: {
        url?: string;
        text?: string;
        duration?: number;
      };
      avatar?: string;
      carOwn?: number;
      gender?: string;
      height?: number;
      idCard?: string;
      nation?: string;
      photos?: string[];
      school?: string;
      source?: string;
      userID?: number;
      zodiac?: string;
      company?: string;
      homeTown?: string;
      houseOwn?: number;
      kidsTake?: string;
      kidsWant?: string;
      nickName?: string;
      fatherJob?: string;
      incomeMax?: number;
      incomeMin?: number;
      marryPlan?: string;
      motherJob?: string;
      onlyChild?: string;
      religious?: string;
      residence?: string;
      familyType?: string;
      markingTag?: string[];
      aboutFamily?: string;
      description?: string;
      houseRegion?: string;
      loveHistory?: string;
      totalIncome?: string;
      userMarking?: {
        recommendReason?: string;
        heartPortraitTags?: {
          id?: number;
          icon?: string;
          title?: string;
          isHighLight?: boolean;
        }[];
        recommendedUserID?: number;
        recommenderUserID?: number;
        recommendReasonHighLights?: null;
      };
      IDCardPrefix?: string;
      aboutHobbies?: string;
      interestTags?: {
        id?: number;
        url?: string;
        title?: string;
        description?: string;
        isSameWavelength?: boolean;
      }[];
      tattooStatus?: string;
      educationForm?: string;
      maritalStatus?: number;
      smokingStatus?: string;
      aboutCharacter?: string;
      singleDuration?: string;
      backgroundImage?: string;
      educationStatus?: number;
      fatherJobStatus?: string;
      motherJobStatus?: string;
      recommendReason?: string;
      /**
       * 精选推荐理由
       */
      curatedRationale?: {
        /**
         * 小标题
         */
        title?: string;
        /**
         * 配图列表
         */
        imageUrl?: string[];
        /**
         * 详细描述
         */
        description?: string;
      }[];
      /**
       * 重点展示的用户信息
       */
      userSpotlightData?: string[];
      /**
       * 对对方的态度
       */
      attitudeToCounterpart?: {
        /**
         * 小标题
         */
        title?: string;
        /**
         * 详细描述
         */
        description?: string;
      }[];
    };
    /**
     * 是否已认证
     */
    verified?: boolean;
    /**
     * 推荐卡片类型，matchmakerPremium-红娘精选
     */
    recommendStyle?: string;
    /**
     * 红娘头像url
     */
    matchmakerAvatar?: string;
    /**
     * 用户反馈，contactAccepted-想认识
     */
    feedbackConclusion?: string;
    /**
     * 推荐编号
     */
    serveRecommendNumber?: number;
    /**
     * 被推荐用户是否是MVIP
     */
    isRecommendedUserMVip?: boolean;
  };
}

/**
 * 接口【售前快速推荐】推荐卡片数据的请求函数
 * 
 * 
 */
export function getV3MmRecommend(d: GetV3MmRecommendRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3MmRecommendResponse>({
    url: `/v3/mm-recommend`,
    method: 'GET',
    params: {
      pToken: d.pToken
    },
    ...restArg,
  })
}
