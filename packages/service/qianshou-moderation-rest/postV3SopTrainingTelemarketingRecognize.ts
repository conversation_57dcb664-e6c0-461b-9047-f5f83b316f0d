/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【SOP培训】语音文件识别的请求参数 */
export interface PostV3SopTrainingTelemarketingRecognizeRequest {
  /**
   * base64加密过的语音二进制
   */
  message: string;
}

/** 接口【SOP培训】语音文件识别的响应数据 */
export interface PostV3SopTrainingTelemarketingRecognizeResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 语音文件识别后的结果
     */
    message?: string;
  };
}

/**
 * 接口【SOP培训】语音文件识别的请求函数
 * 
 * 
 */
export function postV3SopTrainingTelemarketingRecognize(d: PostV3SopTrainingTelemarketingRecognizeRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3SopTrainingTelemarketingRecognizeResponse>({
    url: `/v3/sop-training/telemarketing/recognize`,
    method: 'POST',
    data: {
      message: d.message
    },
    ...restArg,
  })
}
