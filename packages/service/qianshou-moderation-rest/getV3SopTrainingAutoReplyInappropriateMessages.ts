/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口不合适聊天节点获取的请求参数 */
export interface GetV3SopTrainingAutoReplyInappropriateMessagesRequest {
  page?: number;
  size?: number;
  userID?: string[];
  matchmakerID?: string[];
}

/** 接口不合适聊天节点获取的响应数据 */
export interface GetV3SopTrainingAutoReplyInappropriateMessagesResponse {
  msg?: string;
  code?: number;
  data?: {
    list?: {
      /**
       * 用户id
       */
      userID?: number;
      /**
       * 用户姓名
       */
      userName?: string;
      /**
       * 红娘id
       */
      matchMakerID?: number;
      /**
       * 红娘姓名
       */
      matchMakerName?: string;
      /**
       * 不合适聊天列表
       */
      inappropriateContent?: {
        /**
         * 最近的五句话
         */
        msgs?: {
          /**
           * 消息
           */
          msg?: {
            /**
             * 内容
             */
            content?: string;
          };
          /**
           * 消息id
           */
          msgID?: string;
          /**
           * 消息时间
           */
          msgTime?: number;
          /**
           * 消息类型（目前只支持text）
           */
          msgType?: string;
          /**
           * 发送者id
           */
          senderID?: number;
          /**
           * 发送者姓名
           */
          senderName?: string;
          /**
           * 发送角色（1--客户；2--红娘）
           */
          senderRole?: number;
          /**
           * 发送者微信
           */
          senderWeComID?: string;
        }[];
        /**
         * ai的回复
         */
        aiReply?: string;
        /**
         * 红娘填写的回复
         */
        content?: string;
        /**
         * 上报聊天记录的序号
         */
        lastSeq?: number;
        /**
         * 上报时间
         */
        submitTime?: number;
      }[];
    }[];
    total?: number;
  };
}

/**
 * 接口不合适聊天节点获取的请求函数
 * 
 * 
 */
export function getV3SopTrainingAutoReplyInappropriateMessages(d: GetV3SopTrainingAutoReplyInappropriateMessagesRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingAutoReplyInappropriateMessagesResponse>({
    url: `/v3/sop-training/auto-reply/inappropriate-messages`,
    method: 'GET',
    params: {
      page: d.page,
      size: d.size,
      userID: d.userID,
      matchmakerID: d.matchmakerID
    },
    ...restArg,
  })
}
