/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【培训任务】会话详情的请求参数 */
export interface GetV3SopTrainingAiChatConversationRequest {
  /**
   * 会话id
   */
  conversationID?: number;
  /**
   * 任务Id 服务培训任务列表的id
   */
  taskID?: number;
}

/** 接口【培训任务】会话详情的响应数据 */
export interface GetV3SopTrainingAiChatConversationResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 会话id
     */
    id?: number;
    extra?: {
      /**
       * 首通相关
       */
      firstCall?: {
        /**
         * 分数
         */
        score?: string;
        /**
         * 原因
         */
        reason?: string;
        /**
         * 原文
         */
        report?: string;
        /**
         * 关键词
         */
        keyPoint?: string;
        /**
         * 通话记录
         */
        messages?: {
          /**
           * user:人类 assistant:ai
           */
          role?: string;
          /**
           * 状态
           */
          status?: string;
          /**
           * 对话原文
           */
          content?: string;
          /**
           * 发送时间
           */
          createdTime?: number;
          conversationID?: number;
        }[];
      };
      /**
       * 录音地址
       */
      recordURL?: string;
      /**
       * 会员择偶要求（分割符\n）
       */
      userMateDemand?: string;
      /**
       * 虚拟被动方信息
       */
      mockPassiveUserInfo?: {
        /**
         * 学历
         */
        edu?: string;
        /**
         * 工作
         */
        job?: string;
        /**
         * 学校
         */
        scool?: string;
        /**
         * 头像
         */
        avatar?: string;
        /**
         * 车产
         */
        carOwn?: string;
        /**
         * 身高
         */
        height?: number;
        /**
         * 虚拟被动方id
         */
        userID?: number;
        /**
         * 孩子性别
         */
        kidsSex?: string;
        /**
         * 被动方基本信息(分割符\t)
         */
        baseInfo?: string;
        /**
         * 家乡
         */
        homeTown?: string;
        /**
         * 房产
         */
        houseOwn?: string;
        /**
         * 是否有孩子
         */
        kidsTake?: string;
        /**
         * 性格
         */
        character?: string;
        /**
         * 方便的见面时间
         */
        meetTimes?: number[];
        /**
         * 是否独生子女
         */
        onlyChild?: string;
        /**
         * 现居地
         */
        residence?: string;
        /**
         * 家中排行
         */
        familyRank?: string;
        /**
         * 家庭情况
         */
        familyType?: string;
        /**
         * 婚姻状况
         */
        marryStatus?: string;
        /**
         * 偏好的见面方式
         */
        meetMethods?: number[];
        /**
         * 年收入
         */
        totalIncome?: string;
        /**
         * 见面态度
         */
        meetAttitude?: string;
        /**
         * 户口所在地
         */
        regResidence?: string;
        /**
         * 是否纹身
         */
        tattooStatus?: string;
        /**
         * 教育形式
         */
        educationForm?: string;
        /**
         * 感情经历/单身时间
         */
        emotionDescription?: string;
      };
    };
    /**
     * 使用模型
     */
    model?: string;
    /**
     * 会话标题
     */
    title?: string;
    /**
     * 用户头像
     */
    avatar?: string;
    /**
     * 会话状态
     */
    status?: string;
    /**
     * 业务类型
     */
    bizType?: string;
    /**
     * 服务红娘跟会员聊天mockUserID就是会员id  牵线红娘跟被动方聊天，mockUserID就是被动方id
     */
    mockUserID?: number;
    /**
     * 创建时间
     */
    createdTime?: number;
    /**
     * 红娘id
     */
    matchmakerID?: number;
    /**
     * 虚拟用户详情(分隔符：\t)
     */
    mockUserInfo?: string;
    /**
     * sp id
     */
    systemPromptID?: number;
  };
}

/**
 * 接口【培训任务】会话详情的请求函数
 * 
 * 
 */
export function getV3SopTrainingAiChatConversation(d: GetV3SopTrainingAiChatConversationRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingAiChatConversationResponse>({
    url: `/v3/sop-training/ai-chat/conversation`,
    method: 'GET',
    params: {
      conversationID: d.conversationID,
      taskID: d.taskID
    },
    ...restArg,
  })
}
