/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【客诉安抚录音库】列表的请求参数 */
export interface GetV3SopTrainingComplaintStandardAnswersRecordingsRequest {
  /**
   * 一级分类
   */
  category: string[];
  /**
   * 会员收入
   */
  income: string[];
  /**
   * 会员性别
   */
  gender: string[];
  /**
   *  会员入库渠道
   */
  source: string[];
}

/** 接口【客诉安抚录音库】列表的响应数据 */
export interface GetV3SopTrainingComplaintStandardAnswersRecordingsResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 客诉安抚录音库列表
     */
    list?: {
      /**
       * 客诉安抚录音库ID
       */
      id: number;
      /**
       * 标题
       */
      title: string;
    }[];
  };
}

/**
 * 接口【客诉安抚录音库】列表的请求函数
 * 
 * 
 */
export function getV3SopTrainingComplaintStandardAnswersRecordings(d: GetV3SopTrainingComplaintStandardAnswersRecordingsRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingComplaintStandardAnswersRecordingsResponse>({
    url: `/v3/sop-training/complaint-standard-answers/recordings`,
    method: 'GET',
    params: {
      category: d.category,
      income: d.income,
      gender: d.gender,
      source: d.source
    },
    ...restArg,
  })
}
