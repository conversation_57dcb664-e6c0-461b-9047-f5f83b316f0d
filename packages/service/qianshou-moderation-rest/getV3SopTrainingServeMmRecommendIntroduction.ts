/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【SOP培训】1v1推荐语的请求参数 */
export interface GetV3SopTrainingServeMmRecommendIntroductionRequest {
  /**
   * 用户id（把用户id推荐给其他用户id，在把会员推荐给被动方任务中，指会员）
   */
  userId: number;
  /**
   * 其他用户id（把用户id推荐给其他用户id，在把会员推荐给被动方任务中，指被动方）
   */
  otherUserId: number;
}

/** 接口【SOP培训】1v1推荐语的响应数据 */
export interface GetV3SopTrainingServeMmRecommendIntroductionResponse {
  msg?: string;
  code?: number;
  /**
   * 推荐语
   */
  data?: string;
}

/**
 * 接口【SOP培训】1v1推荐语的请求函数
 * 
 * 
 */
export function getV3SopTrainingServeMmRecommendIntroduction(d: GetV3SopTrainingServeMmRecommendIntroductionRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingServeMmRecommendIntroductionResponse>({
    url: `/v3/sop-training/serve-mm/recommend-introduction`,
    method: 'GET',
    params: {
      userId: d.userId,
      otherUserId: d.otherUserId
    },
    ...restArg,
  })
}
