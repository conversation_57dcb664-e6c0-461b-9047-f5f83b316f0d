/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【绩效】订单确认-导出的请求参数 */
export interface GetV3PerformanceOrderConfirmsExportRequest {
  /**
   * 订单状态，notStarted-未开启，started-已开启，payoff-已发薪
   */
  status?: string[];
  /**
   * 订单支付开始时间，unixNano
   */
  pay_start_time?: number;
  /**
   * 订单支付结束时间，unixNano
   */
  pay_end_time?: number;
  /**
   * 分页页码
   */
  page?: number;
  /**
   * 分页容量
   */
  size?: number;
  /**
   * 用户id
   */
  uid?: number;
  /**
   * 红娘id
   */
  matchmaker_id?: number;
  /**
   * 组织架构id列表
   */
  org_ids?: string[];
  /**
   * 订单开启时间，unixNano
   */
  start_service_time?: number;
}

/** 接口【绩效】订单确认-导出的响应数据 */
export interface GetV3PerformanceOrderConfirmsExportResponse {}

/**
 * 接口【绩效】订单确认-导出的请求函数
 * 
 * 
 */
export function getV3PerformanceOrderConfirmsExport(d: GetV3PerformanceOrderConfirmsExportRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3PerformanceOrderConfirmsExportResponse>({
    url: `/v3/performance/order-confirms/export`,
    method: 'GET',
    params: {
      status: d.status,
      pay_start_time: d.pay_start_time,
      pay_end_time: d.pay_end_time,
      page: d.page,
      size: d.size,
      uid: d.uid,
      matchmaker_id: d.matchmaker_id,
      org_ids: d.org_ids,
      start_service_time: d.start_service_time
    },
    ...restArg,
  })
}
