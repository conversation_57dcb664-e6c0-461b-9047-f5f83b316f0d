/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【SOP培训】获取任务历史推荐记录的请求参数 */
export interface GetV3SopTrainingServeMmHistoryRecommendRecordRequest {
  /**
   * 任务id
   */
  taskId?: number;
}

/** 接口【SOP培训】获取任务历史推荐记录的响应数据 */
export interface GetV3SopTrainingServeMmHistoryRecommendRecordResponse {
  msg?: string;
  code?: number;
  data?: {
    total?: number;
    records?: {
      /**
       * 推荐记录id
       */
      id?: number;
      /**
       * 年龄
       */
      age?: number;
      /**
       * 姓名
       */
      name?: string;
      /**
       * 头像
       */
      avatar?: string;
      /**
       * 身高
       */
      height?: number;
      /**
       * 学历
       */
      education?: string;
      /**
       * 被动方id
       */
      passiveId?: number;
      /**
       * 原因
       */
      failReason?: string;
      /**
       * 婚况
       */
      maritalStatus?: string;
      /**
       * 年收入范围最大值
       */
      incomeRangeMax?: number;
      /**
       * 年收入范围最小值
       */
      incomeRangeMin?: number;
      /**
       * 推荐语
       */
      recommendReason?: string;
      /**
       * 结果
       */
      recommendResult?: boolean;
    }[];
  };
}

/**
 * 接口【SOP培训】获取任务历史推荐记录的请求函数
 * 
 * 
 */
export function getV3SopTrainingServeMmHistoryRecommendRecord(d: GetV3SopTrainingServeMmHistoryRecommendRecordRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingServeMmHistoryRecommendRecordResponse>({
    url: `/v3/sop-training/serve-mm/history-recommend-record`,
    method: 'GET',
    params: {
      taskId: d.taskId
    },
    ...restArg,
  })
}
