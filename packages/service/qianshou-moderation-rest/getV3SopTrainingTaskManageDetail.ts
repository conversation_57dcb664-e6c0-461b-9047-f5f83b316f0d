/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【培训任务】任务管理-任务明细的请求参数 */
export interface GetV3SopTrainingTaskManageDetailRequest {
  /**
   * 培训类型，serveMatchmaker-服务红娘，passiveContactMatchmaker-牵线红娘
   */
  taskType: string;
  /**
   * 组织id列表（数字列表）
   */
  orgId?: string;
  /**
   * 任务添加的开始时间，纳秒时间戳
   */
  startTime?: number;
  /**
   * 任务添加的结束时间，纳秒时间戳
   */
  endTime?: number;
  /**
   * 状态列表（数字列表）
   */
  status?: string[];
  /**
   * 任务类型
   */
  todoType?: string;
}

/** 接口【培训任务】任务管理-任务明细的响应数据 */
export interface GetV3SopTrainingTaskManageDetailResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 数据列表
     */
    list?: {
      /**
       * 任务id
       */
      id?: number;
      extra?: {
        questionId?: number;
      };
      /**
       * 会员信息
       */
      member?: {
        /**
         * 会员名字
         */
        name?: string;
        /**
         * 会员性别
         */
        gender?: number;
      };
      /**
       * 任务状态
       */
      status?: number;
      /**
       * 红娘id
       */
      adminId?: number;
      /**
       * 被动方信息
       */
      passive?: {
        /**
         * 被动方名字
         */
        name?: string;
        /**
         * 被动方性别
         */
        gender?: number;
      };
      /**
       * 信息填写率
       */
      fillRate?: number;
      /**
       * 任务类型，任务类型: serveMatchmaker(服务红娘), passiveContactMatchmaker(牵线红娘)
       */
      taskType?: string;
      /**
       * 待办类型
       */
      todoType?: string;
      /**
       * 红娘姓名
       */
      adminName?: string;
      /**
       * 任务来源
       */
      taskSource?: string;
      createdTime?: number;
      /**
       * 最后一条消息
       */
      lastMessage?: string;
      updatedTime?: number;
      /**
       * 聊天消息数量
       */
      messageCount?: number;
      /**
       * 红娘主管id
       */
      adminLeaderId?: number;
      /**
       * 会话id
       */
      conversationId?: number;
      /**
       * 红娘主管姓名
       */
      adminLeaderName?: string;
      /**
       * 完成结果（状态备注）
       */
      completedResult?: string;
    }[];
    /**
     * 列表总数
     */
    total?: number;
  };
}

/**
 * 接口【培训任务】任务管理-任务明细的请求函数
 * 
 * 
 */
export function getV3SopTrainingTaskManageDetail(d: GetV3SopTrainingTaskManageDetailRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingTaskManageDetailResponse>({
    url: `/v3/sop-training/task-manage/detail`,
    method: 'GET',
    params: {
      taskType: d.taskType,
      orgId: d.orgId,
      startTime: d.startTime,
      endTime: d.endTime,
      status: d.status,
      todoType: d.todoType
    },
    ...restArg,
  })
}
