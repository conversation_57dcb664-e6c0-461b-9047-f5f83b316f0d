/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【绩效】订单确认-修改状态的请求参数 */
export interface PostV3PerformanceOrderConfirmsRequest {
  /**
   * 订单id
   */
  ids?: number[];
  /**
   * 订单状态
   */
  status?: string;
  /**
   * 操作类型
   */
  operation?: string;
  /**
   * 开启订单的时间，unixNano
   */
  start_service_time?: number;
}

/** 接口【绩效】订单确认-修改状态的响应数据 */
export interface PostV3PerformanceOrderConfirmsResponse {
  msg?: string;
  code?: number;
}

/**
 * 接口【绩效】订单确认-修改状态的请求函数
 * 
 * 
 */
export function postV3PerformanceOrderConfirms(d: PostV3PerformanceOrderConfirmsRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3PerformanceOrderConfirmsResponse>({
    url: `/v3/performance/order-confirms`,
    method: 'POST',
    data: {
      ids: d.ids,
      status: d.status,
      operation: d.operation,
      start_service_time: d.start_service_time
    },
    ...restArg,
  })
}
