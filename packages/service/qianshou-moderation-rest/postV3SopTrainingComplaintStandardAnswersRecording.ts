/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【客诉安抚录音库】新增的请求参数 */
export interface PostV3SopTrainingComplaintStandardAnswersRecordingRequest {
  /**
   * 备注
   */
  detail?: string;
  /**
   *  会员ID
   */
  userID?: number;
  /**
   * 一级分类
   */
  category?: string;
  /**
   * 录音文件ID数组
   */
  recordIDs?: string[];
}

/** 接口【客诉安抚录音库】新增的响应数据 */
export interface PostV3SopTrainingComplaintStandardAnswersRecordingResponse {
  msg?: string;
  code?: number;
}

/**
 * 接口【客诉安抚录音库】新增的请求函数
 * 
 * 
 */
export function postV3SopTrainingComplaintStandardAnswersRecording(d: PostV3SopTrainingComplaintStandardAnswersRecordingRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3SopTrainingComplaintStandardAnswersRecordingResponse>({
    url: `/v3/sop-training/complaint-standard-answers/recording`,
    method: 'POST',
    data: {
      detail: d.detail,
      userID: d.userID,
      category: d.category,
      recordIDs: d.recordIDs
    },
    ...restArg,
  })
}
