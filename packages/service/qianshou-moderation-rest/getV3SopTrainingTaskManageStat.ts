/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【培训任务】任务管理-统计列表的请求参数 */
export interface GetV3SopTrainingTaskManageStatRequest {
  /**
   * 培训类型，serveMatchmaker-服务红娘，passiveContactMatchmaker-牵线红娘
   */
  taskType: string;
  /**
   * 组织id列表（数字列表）
   */
  orgId?: string[];
  /**
   * 任务添加的开始时间，纳秒时间戳
   */
  startTime?: number;
  /**
   * 任务添加的结束时间，纳秒时间戳
   */
  endTime?: number;
  /**
   * 状态列表（数字列表）
   */
  status?: string[];
  /**
   * 任务类型
   */
  todoType?: string;
  /**
   * 排序字段，例如按照成功率，则传入succeedRate
   */
  orderBy?: string;
  /**
   * 排序方式，asc-升序，desc-降序
   */
  orderType?: string;
}

/** 接口【培训任务】任务管理-统计列表的响应数据 */
export interface GetV3SopTrainingTaskManageStatResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 红娘id
     */
    adminId?: number;
    /**
     * 统计信息
     */
    taskStat?: {
      /**
       * 任务总量
       */
      total?: number;
      /**
       * 任务失败量
       */
      failed?: number;
      /**
       * 任务成功量
       */
      succeed?: number;
      /**
       * 任务完成量
       */
      completed?: number;
      /**
       * 任务成功率
       */
      succeedRate?: number;
      /**
       * 任务完成率
       */
      completedRate?: number;
    };
    /**
     * 红娘姓名
     */
    adminName?: string;
    /**
     * 入职时间，纳秒
     */
    adminEntryTime?: number;
  }[];
}

/**
 * 接口【培训任务】任务管理-统计列表的请求函数
 * 
 * 
 */
export function getV3SopTrainingTaskManageStat(d: GetV3SopTrainingTaskManageStatRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingTaskManageStatResponse>({
    url: `/v3/sop-training/task-manage/stat`,
    method: 'GET',
    params: {
      taskType: d.taskType,
      orgId: d.orgId,
      startTime: d.startTime,
      endTime: d.endTime,
      status: d.status,
      todoType: d.todoType,
      orderBy: d.orderBy,
      orderType: d.orderType
    },
    ...restArg,
  })
}
