/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【system-prompt】新增/编辑类型的请求参数 */
export interface PostV3SystemPromptsBizTypeRequest {
  /**
   * 类型
   */
  bizType?: string;
  /**
   * 类型描述
   */
  typeName?: string;
}

/** 接口【system-prompt】新增/编辑类型的响应数据 */
export interface PostV3SystemPromptsBizTypeResponse {
  msg?: string;
  code?: number;
}

/**
 * 接口【system-prompt】新增/编辑类型的请求函数
 * 
 * 
 */
export function postV3SystemPromptsBizType(d: PostV3SystemPromptsBizTypeRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<PostV3SystemPromptsBizTypeResponse>({
    url: `/v3/system-prompts/biz-type`,
    method: 'POST',
    data: {
      bizType: d.bizType,
      typeName: d.typeName
    },
    ...restArg,
  })
}
