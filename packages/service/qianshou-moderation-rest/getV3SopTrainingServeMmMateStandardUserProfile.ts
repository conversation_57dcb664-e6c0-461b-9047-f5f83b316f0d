/* eslint-disable */
/* 该文件由脚本自动生成，请勿直接修改 */

import request from '../yapi/request'

/** 接口【SOP培训】获取培训任务推荐的用户资料的请求参数 */
export interface GetV3SopTrainingServeMmMateStandardUserProfileRequest {
  /**
   * 用户id
   */
  userId: number;
}

/** 接口【SOP培训】获取培训任务推荐的用户资料的响应数据 */
export interface GetV3SopTrainingServeMmMateStandardUserProfileResponse {
  msg?: string;
  code?: number;
  data?: {
    /**
     * 基本资料
     */
    info?: {
      age?: number;
      job?: {
        industry?: string;
        position?: string;
      };
      life?: {
        homeTown?: string;
        residence?: string;
        lovePurpose?: string;
      };
      height?: number;
      idCard?: string;
      wealth?: {
        car?: {
          ownType?: number;
        };
        house?: {
          ownType?: number;
          houseAddress?: string;
        };
        income?: string;
      };
      zodiac?: string;
      birthDate?: string;
      education?: {
        level?: number;
      };
      description?: string;
      idCardPrefix?: string;
    };
    /**
     * 头像
     */
    avatar?: string;
    /**
     * 用户id
     */
    userID?: number;
    /**
     * 更多资料
     */
    sideInfo?: {
      notes?: string;
      nation?: string;
      character?: string;
      onlyChild?: string;
      religious?: string;
      familyType?: string;
      loveHistory?: string;
      familyDetails?: string;
      smokingStatus?: string;
      emotionDescription?: string;
    };
    /**
     * 生活照
     */
    lifePhoto?: string[];
    /**
     * 特点标签
     */
    featureTag?: string[];
    /**
     * 兴趣标签
     */
    interestTag?: string[];
  };
}

/**
 * 接口【SOP培训】获取培训任务推荐的用户资料的请求函数
 * 
 * 
 */
export function getV3SopTrainingServeMmMateStandardUserProfile(d: GetV3SopTrainingServeMmMateStandardUserProfileRequest, restArg?: Partial<Parameters<typeof request>[0]>) {
  return request<GetV3SopTrainingServeMmMateStandardUserProfileResponse>({
    url: `/v3/sop-training/serve-mm/mate-standard/user-profile`,
    method: 'GET',
    params: {
      userId: d.userId
    },
    ...restArg,
  })
}
