import { ElNotification, } from "element-plus";
import { reactive, provide, inject, type InjectionKey, } from "vue";
import { useUserInfo } from "./useUserInfo";
import request from '../../yapi/request'
import { admin } from "../admin/admin";
import { type Dayjs } from 'dayjs';
import { getRefreshServiceLog } from "../useServiceLog";
import type { Rule } from 'ant-design-vue/es/form';
import { message } from 'ant-design-vue';


export const contactFeedback = {
  /** 愿意接触 */
  contactAccepted: 'contactAccepted',
  /** 拒绝接触 */
  contactRejected: 'contactRejected',
  /** 未有效联系 */
  unableContact: 'unableContact',
  /** 对方很好, 愿意接触 */
  continueContact: 'continueContact',
  /** 不愿意继续接触 */
  stopContact: 'stopContact',
  /** 对方不错, 但不合适 */
  notSuitable: 'notSuitable',
} as const

export function feedbackNoteLabel(feedback?: string) {
  switch (feedback) {
    case contactFeedback.stopContact:
      return '不愿意继续接触的原因'
    case contactFeedback.notSuitable:
      return '觉得不适合的点'
    case contactFeedback.continueContact:
    default:
      return '对对方的具体印象'
  }
}

type ContinueContactType = typeof contactFeedback.continueContact | typeof contactFeedback.stopContact | typeof contactFeedback.notSuitable;

/** 服务小记反馈内容 */
export interface ServeFeedbackContent {
  key: string
  /** 服务内容类型 */
  types?: string[]
  userId?: string;
  /** 服务内容：沟通卡片推荐反馈 */
  suggest?: {
    /** 是否想接触被动方 */
    feedback?: typeof contactFeedback.contactAccepted | typeof contactFeedback.contactRejected;
    /** 其他沟通细节, 拒绝接触的原因 */
    feedbackNote?: string;
  }
  /** 服务内容：告知帮会员牵线的结果 | 会员牵线 */
  connect?: {
    /** 是否想接触被动方 */
    feedback?: typeof contactFeedback.contactAccepted | typeof contactFeedback.contactRejected;
    /** 会员对牵线结果的反馈 | 其他沟通细节, 拒绝接触的原因 */
    feedbackNote?: string;
  }
  /** 服务内容：回访会员视频约会的感受 */
  dating?: {
    /** 视频聊了哪些话题 */
    topic?: string[]
    /** 其他视频约会细节 */
    topicNote?: string
    /** 视频后，是否愿意继续接触 */
    feedback?: ContinueContactType
    /** 其他沟通细节, 拒绝接触的原因 */
    feedbackNote?: string;
  }
  /** 服务内容：沟通加微信后聊天情况 */
  wechat?: {
    /** 微信聊了多少 */
    count?: string
    countNote?: string
    /** 聊了哪些话题 */
    topic?: string[]
    /** 加微信后，是否愿意继续接触 */
    feedback?: ContinueContactType
    /** 其他沟通细节, 拒绝接触的原因 */
    feedbackNote?: string;
  }
  /** 服务内容：跟进聊天进展并指导 */
  chat?: {
    /** 微信聊了多少 */
    count?: string
    countNote?: string
    /** 聊了哪些话题 */
    topic?: string[]
    /** 加微信后，是否愿意继续接触 */
    feedback?: ContinueContactType
    /** 其他沟通细节, 拒绝接触的原因 */
    feedbackNote?: string;
  }
  /** 服务内容：回访线下见面的感受 */
  meet?: {
    /** 见面聊了哪些话题 */
    topic?: string[]
    /** 其他细节 */
    topicNote?: string
    /** 是否愿意继续接触 */
    feedback?: ContinueContactType
    /** 其他沟通细节, 拒绝接触的原因 */
    feedbackNote?: string;
  }
  /** 服务内容：跟进交往进展 */
  intercourse?: {
    note?: string
  }
  /** 服务内容: 跟进恋爱进展 */
  courtship?: {
    note?: string
  }
}

/** 服务小记提交表单 */
export interface ServeLogForm {
  /** 售后阶段 */
  serveStage?: number;
  /** 下次联系时间 */
  nextCare?: Dayjs;
  /** 补记服务日期 */
  afterthought?: Dayjs;
  /** 是否完成今日服务，true-是，false-否 */
  isDone?: boolean;
  /** 在预约时间前通知我 */
  isAppointment?: boolean;

  /** 是否企业微信沟通 */
  weComChat?: boolean;

  /** 是否涉及被动方 | 是否涉及会员联系用户, 共享限制. 非传给接口数据 */
  hasInvolve?: boolean
  /** 涉及被动方 | 会员联系该用户 */
  involves?: ServeFeedbackContent[]

  /** 服务小记记录 */
  serviceDetail?: string
  /** 恋爱对方ID */
  courtshipPartnerId?: number
}

const formKey: InjectionKey<ServeLogForm> = Symbol("服务小记提交表单");

export function useServeLog() {
  const { userInfo: user, setUserInfo } = useUserInfo()
  const refreshServiceLog = getRefreshServiceLog();

  const form = reactive<ServeLogForm>({
    weComChat: undefined,
    serviceDetail: undefined,

    serveStage: undefined,
    nextCare: undefined,
    afterthought: undefined,
    isAppointment: false,

    isDone: undefined, // 是否完成今日服务，true-是，false-否

    involves: [{ key: new Date().toLocaleString() }], // 涉及被动方
    hasInvolve: true,
  })

  provide(formKey, form)

  /** 更改信息 */
  const serviceNote = async () => {
    // 校验：检查涉及人员中是否包含当前用户
    if (form.involves?.some(item => item.userId === user?.value?.id)) {
      message.warning('沟通的人不能为当前用户');
      return Promise.reject('沟通的人不能为当前用户');
    }

    const involves = form.involves?.map((item) => ({
      ...item,
      key: undefined, // 删除无必要透传数据
      types: undefined, // 删除无必要透传数据
    }))

    await request({
      url: "/v3/service-note/membership",
      method: "post",
      data: {
        ...form,
        hasInvolve: undefined, // 删除无必要透传数据
        nextCare: form.nextCare ? form.nextCare?.valueOf() * 1000000 : undefined,
        afterthought: form.afterthought?.format("YYYY-MM-DD"),
        version: user?.value?.membership?.version,
        userId: user?.value?.id,
        isVIP: !!user?.value?.admin?.id,
        matchmakerId: admin?.value?.id,
        involvePassive: user?.value?.admin?.id ? involves : undefined,
        involveMember: user?.value?.admin?.id ? undefined : involves,
        involves: undefined, // 删除无必要透传数据
      },
    });
    ElNotification.success("更新成功");
    setUserInfo?.();
    refreshServiceLog?.()
  };

  return {
    form,
    serviceNote,
  }
}

/** 服务小记的表单状态 */
export function useServeLogForm() {
  return inject(formKey)
}

export const FormLayout = {
  labelCol: { span: 5, },
  wrapperCol: { span: 19 },
};

export const DatingOptions = [
  { label: '家庭情况', value: '家庭情况' },
  { label: '成长经历', value: '成长经历' },
  { label: '情感经历', value: '情感经历' },
  { label: '收入', value: '收入' },
  { label: '工作内容', value: '工作内容' },
  { label: '生活习惯', value: '生活习惯' },
  { label: '兴趣爱好', value: '兴趣爱好' },
  { label: '人生规划', value: '人生规划' },
  { label: '性格', value: '性格' },
  { label: '价值观', value: '价值观' },
]

export const FeedbackOptions = [
  { label: '对方很好，继续接触', value: contactFeedback.continueContact },
  { label: '对方不错，但不合适', value: contactFeedback.notSuitable },
  { label: '不愿意继续接触', value: contactFeedback.stopContact },
]

export const WechatOptions = [
  { label: '聊了很多话题', value: '聊了很多话题', },
  { label: '只聊了几句', value: '只聊了几句', },
  { label: '没说话', value: '没说话', },
]

export const generateRule: (type: string, item: ServeFeedbackContent['dating'] | ServeFeedbackContent['suggest']) => Rule[] | Rule = (type, item) => {
  switch (type) {
    case 'suggest.feedbackNote':
      return [{
        required: item?.feedback === contactFeedback.contactRejected,
      }]
    case 'connect.feedbackNote':
      return [{
        required: item?.feedback === contactFeedback.contactRejected,
      }]
    case 'dating.feedbackNote':
      return [{
        required: item?.feedback === contactFeedback.stopContact || item?.feedback === contactFeedback.notSuitable,
      }]
    case 'wechat.feedbackNote':
      return [{
        required: item?.feedback === contactFeedback.stopContact || item?.feedback === contactFeedback.notSuitable,
      }]
    case 'chat.feedbackNote':
      return [{
        required: item?.feedback === contactFeedback.stopContact || item?.feedback === contactFeedback.notSuitable,
      }]
    case 'meet.feedbackNote':
      return [{
        required: item?.feedback === contactFeedback.stopContact || item?.feedback === contactFeedback.notSuitable,
      }]
    default:
      return []
  }
}

export const rejectOptions = [
  {
    id: '1',
    label: '外貌',
    children: [
      { id: 'appearance', label: '颜值不喜欢', },
      { id: 'height', label: '身高', },
    ],
  },
  {
    id: '2',
    label: '硬性条件不符',
    children: [
      { id: 'age', label: '年龄', },
      { id: 'edu', label: '学历', },
      { id: 'job', label: '职业', },
      { id: 'income', label: '收入', },
      { id: 'houseStatus', label: '房产', },
      { id: 'houseType', label: '购房地点', },
      { id: 'carStatus', label: '车产', },
      { id: 'sign', label: '属相', },
      { id: 'zodiac', label: '星座', },
      { id: 'residences', label: '现居地', },
      { id: 'homeTowns', label: '家乡', },
      { id: 'regResidences', label: '户口所在地', },
      { id: 'idCardPrefixes', label: '籍贯', },
      { id: 'nation', label: '民族', },
      { id: 'religious', label: '宗教信仰', },
    ],
  },
  {
    id: '3',
    label: '婚姻情感状况不符',
    children: [
      { id: 'marryStatusRange', label: '婚况', },
      { id: 'kidsAccept', label: '子女情况', },
      { id: 'loveHistory', label: '情感经历', },
    ],
  },
  {
    id: '4',
    label: '家庭背景',
    children: [
      { id: 'familyType', label: '家庭情况', },
      { id: 'onlyChild', label: '是否独生子女', },
    ],
  },
  {
    id: '5',
    label: '其他原因',
    children: [
      { id: 'unableToTouch', label: '电话多次联系不上', },
      { id: 'withoutReason', label: '无理由拒绝', },
      { id: 'notSingle', label: '自己非单身（慎选）', },
      { id: 'withoutCredentials', label: '不愿提供证件', },
    ],
  },
]