# useGptRecommend Hook Usage Examples

This document provides examples of how to use the refactored `useGptRecommend` hook for different API endpoints.

## Basic Usage (Original API)

```typescript
import { useGptRecommend } from '@qianshou/service/hooks/user/useGptRecommend';

// Using the default API (backward compatible with original implementation)
const { intro, isLoading, generateRecommendLegacy } = useGptRecommend();

// Call the API with just a user ID
const handleGenerateRecommend = async () => {
  await generateRecommendLegacy('12345');
  console.log(intro.value); // The generated recommendation text
};
```

## SOP Training API Usage

```typescript
import { useGptRecommend } from '@qianshou/service/hooks/user/useGptRecommend';

// Specify the 'sopTraining' API type
const { 
  intro, 
  isLoading, 
  error,
  generateSopTrainingRecommend 
} = useGptRecommend('sopTraining');

// Call the API with both user ID and other user ID (passive party)
const handleGenerateSopRecommend = async () => {
  await generateSopTrainingRecommend('12345', '67890');
  console.log(intro.value); // The generated recommendation text
};
```

## Generic Usage (For Any API Configuration)

```typescript
import { useGptRecommend } from '@qianshou/service/hooks/user/useGptRecommend';

// Specify the API type
const { intro, isLoading, error, generateRecommend } = useGptRecommend('sopTraining');

// Call the generic function with a parameters object
const handleGenericRecommend = async () => {
  await generateRecommend({
    userId: '12345',
    otherUserId: '67890',
    // Any other parameters needed for the API
  });
  
  if (error.value) {
    console.error('Error:', error.value);
  } else {
    console.log('Recommendation:', intro.value);
  }
};
```

## Adding New API Configurations

To add support for additional API endpoints, you can extend the `ApiType` type and add a new configuration to the `apiConfigs` object in the hook implementation.

Example of how to extend the hook for a new API:

```typescript
// Inside useGptRecommend.ts
type ApiType = 'default' | 'sopTraining' | 'newApiType';

const apiConfigs: Record<ApiType, ApiConfig> = {
  default: { /* existing config */ },
  sopTraining: { /* existing config */ },
  newApiType: {
    url: "/path/to/new/api",
    method: "get",
    timeout: 100000,
    responseKey: "customResponseKey",
    requiredParams: ["param1", "param2"],
    errorMessages: {
      missingParams: "Please provide required parameters",
      timeout: "Request timed out"
    }
  }
};
```
