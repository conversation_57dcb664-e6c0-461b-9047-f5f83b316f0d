<script setup lang="ts">
import { ref, watch } from "vue";
import { getFeatureTags, config } from '@qianshou/service';
import { Tabs } from 'ant-design-vue';

const props = defineProps<{
  userId?: number;
  containerClass?: string;
  tagItemClass?: string;
  categoryTitleClass?: string;
}>();

// 心动画像标签数据
const preferTags = ref<{
  dimension: string;
  tags: {
    id: number;
    level: string;
    title: string;
    dimension: string;
  }[];
}[]>([]);

// 获取心动画像标签数据
const fetchPreferTags = async (userId: number) => {
  if (!userId) return;
  try {
    const res = await getFeatureTags(userId);
    if (res?.prefer_tag && res.prefer_tag.length > 0) {
      // 添加一个全部标签分类
      preferTags.value = [
        {
          dimension: "全部",
          tags: res.prefer_tag?.flatMap((x) => x.tags) || [],
        },
      ].concat(res.prefer_tag);
    }
  } catch (e) {
    console.error('获取心动画像标签失败', e);
  }
};

// 监听userId变化
watch(() => props.userId, (newUserId) => {
  console.log("props.userId,",props.userId,)
  if (newUserId) {
    fetchPreferTags(newUserId);
  }
}, { immediate: true });
</script>

<template>
  <div v-if="preferTags.length > 0" :class="containerClass">
    <Tabs tab-position="left" class="prefer-tag-tabs">
      <Tabs.TabPane v-for="category in preferTags" :key="category.dimension"
        :tab="config?.selectionEnum?.preferTagDimension?.find(x => x.value === category.dimension)?.label ?? category.dimension">
        <div class="tab-content">
          <div class="tag-list">
            <div v-for="tag in category.tags" :key="tag.id" :class="['tag-item', tagItemClass]">
              {{ tag.title }}
            </div>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>
  </div>
  <div v-else class="no-tags">暂无心动画像标签数据</div>
</template>

<style scoped>
.prefer-tag-tabs {
  width: 100%;
  height: 100%;
}

.tab-content {
  padding: 0 16px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  background-color: #f0f0f0;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.no-tags {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
}

:deep(.ant-tabs-tab) {
  text-align: left;
  padding: 8px 16px;
}

:deep(.ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}

:deep(.ant-tabs-tabpane) {
  padding: 8px 0;
}
</style>
