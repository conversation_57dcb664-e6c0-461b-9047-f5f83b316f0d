import { ref, computed, provide, inject, Ref, ComputedRef } from 'vue';
import { getV3AiColdReading, postV3AiColdReading, type GetV3AiColdReadingResponse, getIsServe } from '@qianshou/service';
import { message } from 'ant-design-vue';
import { marked } from 'marked';
type AnalysisStatus = 'in-progress' | 'completed';

type User = Required<Required<GetV3AiColdReadingResponse>['data']>['extra']['user'];

type MatchAnalysisContext = {
  show: (id?: number) => Promise<void>;
  drawerVisible: Ref<boolean>;
  parsedFullContent: ComputedRef<string | Promise<string>>;
  title: Ref<string>;
  user: Ref<User | undefined>;
  otherUser: Ref<User | undefined>;
  status: Ref<AnalysisStatus | undefined>;
  generateMatchAnalysis: () => Promise<void>;
  fetchMatchAnalysis: (id?: number) => Promise<void>;
};

export function useMatchAnalysis(userID?: number) {
  const drawerVisible = ref(false);
  const matchAnalysisData = ref<string | null>(null);
  const title = ref('匹配分析');

  const status = ref<AnalysisStatus>();


  const user = ref<User>();
  const otherUser = ref<User>();

  const parsedFullContent = computed(() => {
    if (!matchAnalysisData.value) return '';
    return marked.parse(matchAnalysisData.value); // 假设数据已经是HTML格式
  });

  const isServe = getIsServe();
  const otherUserId = ref<number>();

  const reset = () => {
    status.value = undefined;
    matchAnalysisData.value = null;
    user.value = undefined;
    otherUser.value = undefined;
  }

  const show = async (id?: number) => {
    if (!id) return message.error('请提供双向的id')
    reset()
    otherUserId.value = id;
    await fetchMatchAnalysis(id);
    drawerVisible.value = true;
  };

  const fetchMatchAnalysis = async (id?: number) => {
    if (!userID) {
      message.warning('用户ID不存在');
      return;
    }
    try {
      const res = await getV3AiColdReading({
        userID: userID,
        type: 'matching-analysis',
        bizType: isServe?.value ? 'serve' : 'sale',
        otherUserID: id ?? otherUserId.value,
      });

      status.value = res.data?.status as AnalysisStatus;
      if (res.data?.matchingAnalysis) {
        matchAnalysisData.value = res.data?.matchingAnalysis;
      }

      const userData = res.data?.extra?.user || {};
      const otherUserData = res.data?.extra?.otherUser || {};

      user.value = userData;
      otherUser.value = otherUserData;
    } catch (error) {
      console.error('获取匹配分析数据失败', error);
    }
  };

  const generateMatchAnalysis = async () => {
    if (!userID) {
      message.warning('用户ID不存在');
      return;
    }

    try {
      await postV3AiColdReading({
        userID: userID,
        type: 'matching-analysis',
        bizType: isServe?.value ? 'serve' : 'sale',
        otherUserID: otherUserId.value,
      });

      status.value = 'in-progress';
      message.success('重新生成已提交');
    } catch { }
  };



  provide<MatchAnalysisContext>('matchAnalysis', {
    show,
    drawerVisible,
    parsedFullContent,
    title,
    user,
    otherUser,
    status,
    generateMatchAnalysis,
    fetchMatchAnalysis,
  });

  return {
    show
  }
}

export function useMatchAnalysisInject(): MatchAnalysisContext | undefined {
  return inject<MatchAnalysisContext>('matchAnalysis');
}
