<template>
  <Drawer v-model:visible="drawerVisible" :title="title" width="450" :zIndex="20000" placement="right">
    <div class="user-info">
      <div class="user">
        <img :src="user?.avatar" alt="用户头像" class="avatar" />
        <p>当前用户(ID:{{ user?.userID }})</p>
        <p>
          {{ user?.nickName || '未知昵称' }}
          <span v-if="user?.familyName">({{ user?.familyName }})</span>
          <span v-if="user?.gender"> · {{ user?.gender }}</span>
          <span v-if="user?.age"> · {{ user?.age }}岁</span>
        </p>
      </div>
      <div class="other-user">
        <img :src="otherUser?.avatar" alt="推荐用户头像" class="avatar" />
        <p>推荐对象(ID:{{ otherUser?.userID }})</p>
        <p>
          {{ otherUser?.nickName || '未知昵称' }}
          <span v-if="otherUser?.familyName">({{ otherUser?.familyName }})</span>
          <span v-if="otherUser?.gender"> · {{ otherUser?.gender }}</span>
          <span v-if="otherUser?.age"> · {{ otherUser?.age }}岁</span>
        </p>
      </div>
    </div>
    <div v-html="parsedFullContent"></div>
    <div v-if="status === 'in-progress'" class="empty-body">
      <span>生成中...</span>
      <el-button type="primary" link @click="() => fetchMatchAnalysis(otherUser?.userID)" class="refresh-btn">
        <i class="el-icon-refresh"></i> 刷新
      </el-button>
    </div>
    <div v-else class="empty-body">
      <el-button @click="generateMatchAnalysis" type="primary" link>用户已更新? 重新生成</el-button>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
import { Drawer } from 'ant-design-vue';
import { useMatchAnalysisInject } from './useMatchAnalysis';

const matchAnalysis = useMatchAnalysisInject();
const { drawerVisible, parsedFullContent, title, user, otherUser, status, generateMatchAnalysis, fetchMatchAnalysis } = matchAnalysis!;

</script>

<style scoped>
.user-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.user,
.other-user {
  text-align: center;
  flex: 1;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 10px;
}

.empty-body {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  gap: 10px;
}

.refresh-btn {
  margin-left: 10px;
}
</style>
