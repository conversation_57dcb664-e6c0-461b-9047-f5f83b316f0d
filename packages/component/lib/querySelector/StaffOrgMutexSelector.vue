<script setup lang="ts">
import {  computed, watch, ref } from 'vue';
import StaffSelect from './StaffSelect.vue';
import OrganizationCascader from './OrganizationCascader.vue';
import { useStaffOrgMutex } from '@qianshou/service';

interface Props {
  // v-model:staffId
  staffId?: number | string | undefined;
  // v-model:orgIds
  orgIds?: string[] | number[] | undefined;
  // 传递给 StaffSelect 的 props
  staffSelectProps?: Record<string, any>;
  // 传递给 OrganizationCascader 的 props
  orgCascaderProps?: Record<string, any>;
  // 组件布局方向
  direction?: 'horizontal' | 'vertical';
  // 是否显示红娘选择器
  showStaffSelect?: boolean;
  // 是否显示组织架构选择器
  showOrgSelect?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showStaffSelect: true,
  showOrgSelect: true,
  direction: 'horizontal'
});

const emit = defineEmits(['update:staffId', 'update:orgIds']);

// 内部双向绑定
const innerStaffId = ref(props.staffId);
const innerOrgIds = ref(props.orgIds);

// 互斥逻辑
useStaffOrgMutex({
  staffId: innerStaffId,
  orgIds: innerOrgIds
});

// 外部v-model同步
watch(() => props.staffId, v => { if (v !== innerStaffId.value) innerStaffId.value = v; });
watch(() => props.orgIds, v => { if (v !== innerOrgIds.value) innerOrgIds.value = v; });
watch(innerStaffId, v => emit('update:staffId', v));
watch(innerOrgIds, v => emit('update:orgIds', v));

const direction = computed(() => props.direction ?? 'horizontal');
</script>

<template>
  <div :style="direction === 'horizontal' ? 'display:flex;gap:8px;align-items:center;' : 'display:block;'">
    <OrganizationCascader
      v-if="showOrgSelect"
      v-bind="orgCascaderProps"
      v-model="innerOrgIds"
      style="min-width:200px;"
    />
    <StaffSelect
      v-if="showStaffSelect"
      v-bind="staffSelectProps"
      v-model="innerStaffId"
      style="min-width:200px;"
    />
  </div>
</template> 